/*
******        FileName    :   chat.go
******        Describe    :   此文件主要用于聊天服务控制器
******        Date        :   2025-04-03
******        Author      :   TangJinFei
******        Copyright   :   Guangzhou AiYunJi Inc.
******        Note        :   聊天服务相关的控制器实现
 */

package chat

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/public/tools"
	"ayj_chat_back/internal/service/chat"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gorilla/websocket"
	"net/http"
)

// 实时聊天控制器
type ctrlChat struct {
	server *server.ServerChat
}

var ChatApi = ctrlChat{}

/*
// 发送消息
func (c *ctrlChat) SendMessage(ctx context.Context, req *chat.SendMessageReq) (res *chat.SendMessageRes, err error) {
	res, err = c.server.SendMessage(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取消息列表
func (c *ctrlChat) GetMessageList(ctx context.Context, req *chat.GetMessageListReq) (res *chat.GetMessageListRes, err error) {
	res, err = c.server.GetMessageList(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 标记消息已读
func (c *ctrlChat) MarkMessageRead(ctx context.Context, req *chat.MarkMessageReadReq) (res *chat.MarkMessageReadRes, err error) {
	res, err = c.server.MarkMessageRead(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取未读消息数
func (c *ctrlChat) GetUnreadCount(ctx context.Context, req *chat.GetUnreadCountReq) (res *chat.GetUnreadCountRes, err error) {
	res, err = c.server.GetUnreadCount(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 创建群组
func (c *ctrlChat) CreateGroup(ctx context.Context, req *chat.CreateGroupReq) (res *chat.CreateGroupRes, err error) {
	res, err = c.server.CreateGroup(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取群组信息
func (c *ctrlChat) GetGroupInfo(ctx context.Context, req *chat.GetGroupInfoReq) (res *chat.GetGroupInfoRes, err error) {
	res, err = c.server.GetGroupInfo(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 获取群组成员列表
func (c *ctrlChat) GetGroupMembers(ctx context.Context, req *chat.GetGroupMembersReq) (res *chat.GetGroupMembersRes, err error) {
	res, err = c.server.GetGroupMembers(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// WebSocket连接处理
func (c *ctrlChat) WebSocketHandler(r *ghttp.Request) {
	// 获取token参数
	token := r.GetQuery("token")
	if token == "" {
		r.Response.WriteStatus(401, []byte("未授权的访问"))
		return
	}

	// 升级为WebSocket连接
	ws, err := r.WebSocket()
	if err != nil {
		r.Response.WriteStatus(500, []byte(err.Error()))
		return
	}

	// 交由WebSocket服务处理
	c.server.HandleWebSocket(r.Context(), ws, token)
}*/

// 将http 升级为 websocket
var wsUpGrader = websocket.Upgrader{
	// CheckOrigin allows any origin in development
	// In production, implement proper origin checking for security
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
	// Error handler for upgrade failures
	Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
		// Implement error handling logic here
	},
}

// 聊天websocket长连接 登录请求
// 此接口用于建立实时聊天的WebSocket长连接
// 客户端需要在请求头中携带Authorization: AyjChat <token>格式的认证信息
func (c *ctrlChat) ChatWsLogin(ctx context.Context, req *chat.ChatWsLoginReq) (res *chat.ChatWsLoginRes, err error) {
	// 从上下文中获取请求对象
	r := g.RequestFromCtx(ctx)

	// 将http 升级为 websocket
	ws, err := wsUpGrader.Upgrade(r.Response.Writer, r.Request, nil)
	if err != nil {
		ctx := r.Context()
		response.Error(ctx, err.Error())
		return
	}
	err, nRet := c.server.HandelWsLogin(r, ws)

	// 使用websocket 发送响应
	if 0 != nRet {
		_ = response.WsError(ws, err.Error(), nRet)
		_ = ws.Close()
	} else {
		msgInfoRet := &chat.MsgBasicInfo{
			//	消息基本信息
			MsgType: consts.MsgType_System,

			//	消息发送者信息
			SenderId:      consts.MsgType_System,
			SendTimestamp: tools.GtimeToTimestamp(gtime.Now()),
			SenderNick:    "",
			SenderAvatar:  "",

			//	信息内容
			MsgContentFmt:  1,
			MsgContentType: consts.MsgContentType_ChatWsLoginOk,
			MsgContent:     "",
			Extra:          "",

			//	接受者信息
			ConvId:   "",
			ConvType: consts.ReceiverType_Private,
		}
		_ = response.WsSuccess(ws, "Ws 连接成功", msgInfoRet)
	}

	return nil, nil
}

// ChatWsLogin 处理WebSocket连接请求
// 此接口用于建立实时聊天的WebSocket长连接
// 客户端需要在请求头中携带Authorization: AyjChat <token>格式的认证信息
/*func (c *ctrlChat) ChatWsLogin(r *ghttp.Request) {
	// 将HTTP请求升级为WebSocket连接
	ws, err := wsUpGrader.Upgrade(r.Response.Writer, r.Request, nil)
	if err != nil {
		r.Response.WriteStatus(http.StatusInternalServerError, []byte("WebSocket连接建立失败: "+err.Error()))
		return
	}

	// 处理WebSocket登录认证
	err, nRet := c.server.HandelWsLogin(r, ws)

	// 根据认证结果发送响应
	if 0 != nRet {
		response.WsError(ws, err.Error(), nRet)
		_ = ws.Close()
	} else {
		response.WsSuccess(ws, "WebSocket连接成功", nil)
	}
}*/
