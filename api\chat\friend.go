/*
******		FileName	:	friend.go
******		Describe	:	此文件主要用于好友管理的API接口定义
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   好友管理API接口
 */

package chat

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 好友信息
type FriendInfo struct {
	UserId         string `json:"user_id" dc:"用户ID"`
	UserNick       string `json:"user_nick" dc:"用户昵称"`
	UserAvatar     string `json:"user_avatar" dc:"用户头像"`
	Remark         string `json:"remark" dc:"好友备注名"`
	RelationStatus int    `json:"relation_status" dc:"好友关系状态，0 不是好友，1正常，2不看他，3不让他看我, 4互不相看"`
	IsStar         bool   `json:"is_star" dc:"是否星标好友"`
	IsTop          bool   `json:"is_top" dc:"是否置顶好友"`
	MessageMute    bool   `json:"message_mute" default:"false" dc:"是否消息免打扰"`
	CreatedAt      string `json:"created_at" time:"2006-01-02 15:04:05" dc:"成为好友时间"`
}

// 好友分组信息
type FriendGroupInfo struct {
	GroupId     int64  `json:"group_id" dc:"分组ID"`
	GroupName   string `json:"group_name" dc:"分组名称"`
	GroupOrder  int    `json:"group_order" dc:"分组排序"`
	MemberCount int    `json:"member_count" dc:"分组成员数"`
}

// 1、获取好友列表请求
type GetFriendListReq struct {
	g.Meta `path:"/friend-list" tags:"好友管理" method:"get" summary:"1、获取好友列表"`

	NotHasLabel bool `p:"not_has_label" default:"false" dc:"是否获取未带标签的好友,否则是全部好友"`
	OnlyHasStar bool `p:"only_has_star" default:"false" dc:"是否只获取标星好友,否则是全部好友"`
}

// 1、获取好友列表响应
type GetFriendListRes struct {
	g.Meta `mime:"application/json" example:"string"`

	List       []FriendInfo `json:"list" dc:"好友列表"`
	TotalCount int          `json:"total_count" dc:"好友总数"`
}

// 2、获取好友信息请求
type GetFriendInfoReq struct {
	g.Meta `path:"/friend-info" tags:"好友管理" method:"get" summary:"2、获取好友信息"`

	FriendId string `v:"required|length:1,32#请输入好友id(friend_id)|好友id长度不能超过32个字符" p:"friend_id" dc:"好友唯一id"`
}

// 2、获取好友信息回复
type GetFriendInfoRes struct {
	//	用户基本信息
	UserId        string `json:"user_id" dc:"用户id"`
	UserPhone     string `json:"user_phone" dc:"用户手机号"`
	UserAvatar    string `json:"user_avatar" dc:"用户头像"`
	UserNick      string `json:"user_nick" dc:"用户昵称"`
	UserSex       int    `json:"user_sex" dc:"用户性别 1男 ; 2女"`
	UserRoleType  int    `json:"user_role_type"  dc:"用户角色类型"`
	UserBirth     string `json:"user_birth"  dc:"用户出生年月; 2000-10-10"` //	出生年月
	UserEmail     string `json:"user_email" dc:"用户邮箱"`
	UserSignature string `json:"user_signature" dc:"用户签名"`
	UserStatus    int    `json:"user_status" default:"0" dc:"用户状态，如努力，奋斗，加油等"`
	UserRegion    int    `json:"user_region" default:"0" dc:"用户所在区域"`

	//	添加好友后的信息
	Remark         string `json:"remark" dc:"好友备注名"`
	IsStar         bool   `json:"is_star" default:"false" dc:"是否星标好友"`
	IsTop          bool   `json:"is_top" default:"false" dc:"是否置顶好友"`
	MessageMute    bool   `json:"message_mute" default:"false" dc:"是否消息免打扰"`
	RelationStatus int    `json:"relation_status"  default:"0"  dc:"好友关系状态 0不是好友; 1正常;2不看他;3不让他看我;4互不相看"`
	AddTime        string `json:"add_at" time:"2006-01-02" dc:"成为好友时间"`

	//	添加好友方式
	RequestType   int `json:"request_type" dc:"申请方向，1对方加的我，2我加的对方"`
	RequestSource int `json:"request_source" default:"false" dc:"1搜索，2群聊，3名片，4二维码， 5附近的人"`

	//	好友标签信息
	LabelList []FriendLabelBasicInfo `json:"label_list" dc:"好友标签列表"`

	//	是否添加到黑名单
	IsBlocked   bool   `json:"is_blocked" dc:"是否拉入黑名单"`
	BlockReason string `json:"block_reason" dc:"拉入黑名单原因"`
}

// 3、添加好友请求
type AddFriendReq struct {
	g.Meta `path:"/friend-add" tags:"好友管理" method:"post" summary:"3、添加好友"`

	UserId        string `v:"required|length:1,32#请输入好友id|好友id长度不能超过32个字符" p:"user_id" dc:"要添加的用户ID"`
	RequestMsg    string `v:"length:0,100#申请原因长度不能超过100个字符" p:"request_msg" dc:"申请消息"`
	RequestSource int    `p:"request_source" default:"1" dc:"申请来源，1搜索，2群聊，3名片，4二维码, 5附近的人"`
}

// 3、添加好友响应
type AddFriendRes struct {
	g.Meta `mime:"application/json" example:"string"`

	AddStatus int    `json:"add_status" dc:"1:发送申请成功, 2:已经是好友, 3:直接添加成功"`
	Message   string `json:"message" dc:"提示消息"`
}

// 4、处理好友申请请求
type HandleFriendRequestReq struct {
	g.Meta `path:"/friend-handle-request" tags:"好友管理" method:"post" summary:"4、处理好友申请"`

	RequestId    int    `v:"required|length:1,32#请输入好友申请id(request_id)|id长度不能超过32个字符" p:"request_id" dc:"申请记录ID"`
	HandleStatus int    `v:"required#请输入处理状态" p:"handle_status" dc:"1未处理，2同意，3拒绝，4忽略"`
	HandleMsg    string `v:"length:0,100#处理信息长度不能超过100个字符"  p:"handle_msg" dc:"处理消息"`
}

// 4、处理好友申请响应
type HandleFriendRequestRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"提示消息"`
}

// 5、获取好友申请列表请求
type GetFriendRequestsReq struct {
	g.Meta `path:"/friend-requests" tags:"好友管理" method:"get" summary:"5、获取好友申请列表" describe:"request_type=1,status = 1 可获取仅收到的申请 "`

	RequestType int `p:"request_type" default:"0" dc:"申请方向，0全部，1收到的申请，2发出的申请"`
	Status      int `p:"status" default:"0" dc:"申请状态，0全部，1未处理，2已同意，3已拒绝，4已忽略"`

	Page int `p:"page" dc:"页码，从1开始"`
	Size int `p:"size" dc:"每页数量,每页最多100"`
}

// 5、获取好友申请列表响应
type GetFriendRequestsRes struct {
	g.Meta `mime:"application/json" example:"string"`

	List       []FriendRequestInfo `json:"list" dc:"申请列表"`
	TotalCount int                 `json:"total_count" dc:"总申请数"`
}

// 好友申请信息
type FriendRequestInfo struct {
	RequestId   int `json:"request_id" dc:"申请记录ID"`
	RequestType int `json:"request_type" dc:"申请方向，1收到的申请，2发出的申请"`

	FriendId     string `json:"friend_id" dc:"申请者用户ID"`
	FriendNick   string `json:"friend_nick" dc:"申请者用户名称"`
	FriendAvatar string `json:"friend_avatar" dc:"申请者用户头像"`

	RequestMsg    string `json:"request_msg" dc:"申请消息"`
	RequestSource int    `json:"request_source" dc:"申请来源，1搜索，2群聊，3名片，4二维码，5附近的人"`
	HandleStatus  int    `json:"handle_status" dc:"处理状态，1待处理，2同意，3拒绝，4忽略"`
	HandleMsg     string `json:"handle_msg" dc:"处理消息"`

	HandleTime string `json:"handle_time" time:"2006-01-02 15:04:05" dc:"处理时间"`
	CreatedAt  string `json:"created_at" time:"2006-01-02 15:04:05" dc:"申请时间"`
}

// 6、删除好友请求显示请求
type FriendRequestDelShowReq struct {
	g.Meta    `path:"/friend-delete_request_show" tags:"好友管理" method:"post" summary:"6、删除好友请求显示"`
	RequestId int `v:"required|length:1,32#请输入好友申请记录id(request_id)|id长度不能超过32个字符" p:"request_id" dc:"申请记录ID"`
}

// 6、删除好友请求显示响应
type FriendRequestDelShowRes struct {
	g.Meta `mime:"application/json" example:"string"`

	RequestId bool `json:"request_id" dc:"申请记录ID"`
	Success   bool `json:"success" dc:"是否成功"`
}

// 7、删除好友请求
type DeleteFriendReq struct {
	g.Meta `path:"/friend-delete" tags:"好友管理" method:"post" summary:"7、删除好友"`

	FriendId     string `v:"required" p:"friend_id" dc:"要删除的好友ID"`
	IsDeleteBoth bool   `p:"is_delete_both" default:"false" dc:"是否双边删除, 默认单边删除"`
}

// 7、删除好友响应
type DeleteFriendRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"提示消息"`
}

// 8、更新好友信息请求
type UpdateFriendInfoReq struct {
	g.Meta `path:"/friend-update" tags:"好友管理" method:"post" summary:"8、更新好友信息"`

	FriendId       string `v:"required" p:"friend_id" dc:"好友ID"`
	Remark         string `p:"remark" dc:"好友备注名"`
	RelationStatus int    `v:"between:1,4#好友状态在1-4之间" p:"relation_status" dc:"好友关系状态，1正常，2不看他，3不让他看我，互不相看"`
	IsStar         bool   `p:"is_star" dc:"是否星标好友"`
	IsTop          bool   `p:"is_top" dc:"是否置顶好友"`
	MessageMute    bool   `p:"message_mute" dc:"是否消息免打扰"` //	是否消息免打扰
}

// 8、更新好友信息响应
type UpdateFriendInfoRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Remark         string `json:"remark" dc:"好友备注名"`
	RelationStatus int    `json:"relation_status" dc:"好友关系状态，1正常，2不看他，3不让他看我，互不相看"`
	IsStar         bool   `json:"is_star" dc:"是否星标好友"`
	IsTop          bool   `json:"is_top" dc:"是否置顶好友"`
	MessageMute    bool   `json:"message_mute" dc:"是否消息免打扰"` //	是否消息免打扰
}

// 9、创建好友标签请求
type CreateFriendLabelReq struct {
	g.Meta `path:"/friend-label-create" tags:"好友管理" method:"post" summary:"9、创建好友标签"`

	LabelName string `v:"required|length:1,20#请输入标签名称|标签名称长度不能超过20个字符" p:"Label_name"  dc:"标签名称(最多20个字符)"`
}

// 9、创建好友标签响应
type CreateFriendLabelRes struct {
	g.Meta `mime:"application/json" example:"string"`

	LabelId    string `json:"label_id"  dc:"标签id"`
	LabelName  string `json:"label_name"  dc:"标签名称"`
	LabelOrder int    `json:"label_order" dc:"标签排序"`
}

// 10、更新好友标签请求
type UpdateFriendLabelReq struct {
	g.Meta `path:"/friend-label-update" tags:"好友管理" method:"post" summary:"10、更新好友标签"`

	LabelId   string `v:"required|length:1,32#请输入标签(label_id)|标签id长度不能超过32个字符" p:"label_id" dc:"标签id"`
	LabelName string `p:"label_name" v:"length:1,20#标签名称长度不能超过20个字符" dc:"标签名称(最多20个字符)"`
}

// 10、更新好友标签响应
type UpdateFriendLabelRes struct {
	g.Meta `mime:"application/json" example:"string"`

	LabelId   string `json:"label_id"  dc:"标签id"`
	LabelName string `json:"label_name"  dc:"标签名称"`
}

// 11、获取好友所有标签列表请求
type GetFriendLabelsReq struct {
	g.Meta `path:"/friend-labels" tags:"好友管理" method:"get" summary:"11、获取好友标签列表"`

	LabelId          string `p:"label_id" default:"" dc:"标签id, id='',获取所有标签,否则获取指定标签好友列表"`
	IsNeedFriendList bool   `p:"is_need_friend_list" default:"false" dc:"是否需要好友列表"`
}

// 标签基本信息
type FriendLabelBasicInfo struct {
	LabelId     string `json:"label_id"  dc:"标签id"`
	LabelName   string `json:"label_name"  dc:"标签名称"`
	LabelOrder  int    `json:"label_order" dc:"标签排序"`
	FriendCount int    `json:"friend_count" dc:"好友数量"`
}

// 好友标签信息
type FriendLabelInfo struct {
	LabelInfo FriendLabelBasicInfo `json:"label_info"  dc:"标签基本信息"`

	FriendList []FriendInfo `json:"friend_list" dc:"好友列表"`
}

// 11、获取好友所有标签列表响应
type GetFriendLabelsRes struct {
	g.Meta `mime:"application/json" example:"string"`

	List []FriendLabelInfo `json:"list" dc:"标签列表"`
}

// 12、删除好友标签请求，并删除标签所关联的好友
type DeleteFriendLabelReq struct {
	g.Meta `path:"/friend-label-delete" tags:"好友管理" method:"post" summary:"12、删除好友标签"`

	LabelIds []string `v:"required#请输入要删除的标签列表" p:"label_ids" dc:"标签id列表"`
}

// 12、删除好友标签响应
type DeleteFriendLabelRes struct {
	g.Meta `mime:"application/json" example:"string"`

	LabelIds []string `json:"label_ids" dc:"删除的标签id列表"`
}

// 13、好友批量关联标签请求 (直接覆盖好友所有标签)
type RelateFriendLabelReq struct {
	g.Meta `path:"/friend-label-relate" tags:"好友管理" method:"post" summary:"13、为好友关联标签(覆盖)"`

	FriendId string   `v:"required|length:1,32#请输入好友id(friend_id)|好友id长度不能超过32个字符" p:"friend_id" dc:"好友id"`
	LabelIds []string `v:"required#请输入标签id列表" p:"label_ids" dc:"标签id列表"`
}

// 13、好友批量关联标签响应
type RelateFriendLabelRes struct {
	g.Meta `mime:"application/json" example:"string"`

	FriendId string   `p:"friend_id" dc:"好友id"`
	LabelIds []string `p:"label_ids" dc:"标签id列表"`
}

// 14、标签批量关联好友请求 (增加模式)
type LabelRelateFriendReq struct {
	g.Meta `path:"/label-relate-friend" tags:"好友管理" method:"post" summary:"14、标签批量关联好友(增加)"`

	LabelId   string   `v:"required|length:1,32#请输入标签(label_id)|标签id长度不能超过32个字符" p:"label_id" dc:"标签id"`
	FriendIds []string `v:"required#请输入好友列表" p:"friend_ids" dc:"好友id列表"`
}

// 14、标签批量关联好友 响应
type LabelRelateFriendRes struct {
	g.Meta `mime:"application/json" example:"string"`

	LabelIds string   `json:"label_id" dc:"好友id"`
	FriendIs []string `json:"friend_ids" dc:"成功关联的好友id列表"`
}

// 15、批量好友关联批量标签请求(增加)
type FriendsRelateLabelsReq struct {
	g.Meta `path:"/friends-relate-labels" tags:"好友管理" method:"post" summary:"15、批量好友关联批量标签(增加)"`

	FriendIds []string `v:"required#请输入好友id列表" p:"friend_ids" dc:"好友id"`
	LabelIds  []string `v:"required#请输入标签id列表" p:"label_ids" dc:"标签id列表"`
}

// 15、批量好友关联批量标签回复(增加)
type FriendsRelateLabelsRes struct {
	g.Meta `mime:"application/json" example:"string"`

	LabelIds  []string `json:"label_ids" dc:"标签id列表"`
	FriendIds []string `json:"friend_ids" dc:"成功关联的好友id列表"`
	Success   bool     `json:"success" dc:"是否成功"`
	Message   string   `json:"message" dc:"提示消息"`
}

// 16、好友标签排序请求
type FriendLabelSortReq struct {
	g.Meta `path:"/friend-label-sort" tags:"好友管理" method:"post" summary:"16、标签排序(所有标签传入)"`

	LabelOrderList []LabelSortInfo `v:"required#请输入标签排序列表" p:"label_order_list" dc:"标签排序列表"`
}

// 好友标签排序
type LabelSortInfo struct {
	LabelId    string `json:"label_id"  dc:"标签id"`
	LabelOrder int    `json:"label_order" dc:"标签排序"`
}

// 16、标签排序回应
type FriendLabelSortRes struct {
	g.Meta `mime:"application/json" example:"string"`

	LabelOrderList []LabelSortInfo `json:"label_order_list" dc:"标签排序列表"`
}

// 17、标签批量取消关联好友请求 (返回取消关联的好友列表)
type LabelUnrelateFriendReq struct {
	g.Meta `path:"/label-unrelate-friend" tags:"好友管理" method:"post" summary:"17、标签批量取消关联好友"`

	LabelId   string   `v:"required|length:1,32#请输入标签(label_id)|标签id长度不能超过32个字符" p:"label_id" dc:"标签id"`
	FriendIds []string `v:"required#请输入好友列表" p:"friend_ids" dc:"好友id列表"`
}

// 17、标签批量取消关联好友 响应
type LabelUnrelateFriendRes struct {
	g.Meta `mime:"application/json" example:"string"`

	LabelId  string   `json:"label_id" dc:"好友id"`
	FriendIs []string `json:"friend_ids" dc:"成功取消关联的好友id列表"`
}

// 18、拉黑用户请求
type BlockUserReq struct {
	g.Meta `path:"/friend-block" tags:"好友管理" method:"post" summary:"18、黑名单-拉黑好友"`

	FriendId    string `v:"required|length:1,32#请输入标签(label_id)|标签id长度不能超过32个字符" p:"friend_id" dc:"要拉黑的用户ID"`
	BlockReason string `v:"length:0,32#拉黑原因长度不能超过32个字符" p:"block_reason" dc:"拉黑原因"`
	BlockType   int    `p:"block_type" default:"1" dc:"拉黑类型，1完全拉黑，2仅不看朋友圈"`
}

// 18、拉黑用户响应
type BlockUserRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"提示消息"`
}

// 19、取消拉黑用户请求
type UnblockUserReq struct {
	g.Meta `path:"/friend-unblock" tags:"好友管理" method:"post" summary:"19、黑名单-取消拉黑"`

	UserId string `v:"required|length:1,32#请输入用户id(user_id)|id长度不能超过32个字符" p:"user_id" dc:"要取消拉黑的用户ID"`
}

// 19、取消拉黑用户响应
type UnblockUserRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"提示消息"`
}

// 20、获取黑名单列表请求
type GetBlockListReq struct {
	g.Meta `path:"/friend-blocklist" tags:"好友管理" method:"get" summary:"20、黑名单-列表获取"`

	Page int `p:"page" dc:"页码，从1开始"`
	Size int `p:"size" dc:"每页数量,没有最多100个"`
}

// 20、获取黑名单列表响应
type GetBlockListRes struct {
	g.Meta `mime:"application/json" example:"string"`

	List       []BlockUserInfo `json:"list" dc:"黑名单列表"`
	TotalCount int             `json:"total_count" dc:"黑名单总数"`
}

// 黑名单用户信息
type BlockUserInfo struct {
	UserId      string `json:"user_id" dc:"用户ID"`
	UserName    string `json:"user_name" dc:"用户名称"`
	UserAvatar  string `json:"user_avatar" dc:"用户头像"`
	BlockReason string `json:"block_reason" dc:"拉黑原因"`
	BlockType   int    `json:"block_type" dc:"拉黑类型，1完全拉黑，2仅不看朋友圈"`
	CreatedAt   string `json:"created_at" time:"2006-01-02 15:04:05" dc:"拉黑时间, 2006-01-02 15:04:05"`
}
