/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package user

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 1、用户基本信息请求
type UserInfoReq struct {
	g.Meta `path:"/user-info" tags:"三、用户信息" method:"get" summary:"1、用户基本信息"`

	UserId string `v:"required#请输入用户手机号" p:"user_id" dc:"用户唯一uid"  swagger:"环境名称"` //	用户手机号
}

// 1、用户基本信息返回
type UserInfoRes struct {
	g.Meta `mime:"text/html" example:"string"`

	UserId           string `json:"user_id" dc:"用户id"`
	UserPhone        string `json:"user_phone" dc:"用户手机号"`
	UserAvatar       string `json:"user_avatar" dc:"用户头像"`
	UserNick         string `json:"user_nick" dc:"用户昵称"`
	UserSex          int    `json:"user_sex" dc:"用户性别 1男 ; 2女"`
	UserRoleType     int    `json:"user_role_type"  dc:"用户角色类型"`
	UserBirth        string `json:"user_birth"  dc:"用户出生年月; 2000-10-10"` //	出生年月
	UserEmail        string `json:"user_email" dc:"用户邮箱"`
	UserSignature    string `json:"user_signature" dc:"用户签名"`
	UserStatus       int    `json:"user_status" dc:"用户状态，如努力，奋斗，加油等"`
	UserRegion       int    `json:"user_region" dc:"用户所在区域"`
	FriendNeedVerify bool   `json:"friend_need_verify" dc:"加好友是否需要验证，默认需要"` //	加好友是否需要验证
	CreatedAt        string `json:"created_at,time_format=2006-01-02 15:04:05" dc:"创建时间"`
	UpdatedAt        string `json:"updated_at,omitempty" time:"2006-01-02 15:04:05" dc:"更新时间"`
}

// 2、用户信息请求
type UserInfoUpdateReq struct {
	g.Meta `path:"/user-info-update" tags:"三、用户信息" method:"post" summary:"2、用户信息更新"`

	UserId           string `v:"required|length:0,32#请输入uid|用户id最长32个字符" p:"user_id" dc:"用户唯一uid"`
	UserAvatar       string `p:"user_avatar" dc:"用户头像"`
	UserNick         string `v:"length:1,32#用户昵称最多32个字符" p:"user_nick" dc:"用户昵称, 长度1到32个字符"`
	UserSex          int    `p:"user_sex" dc:"用户性别 1男 ; 2女"`
	UserBirth        string `v:"length:0,20#出生日期最多20个字符" p:"user_birth" dc:"用户出生年月; 2000-10-10"` //	出生年月
	UserEmail        string `v:"length:0,32#邮箱最多32个字符" p:"user_email" dc:"用户邮箱"`
	UserSignature    string `v:"length:0,32#签名最多32个字符" p:"user_signature" dc:"用户签名"`
	UserStatus       int    `p:"user_status" dc:"用户状态，如努力，奋斗，加油等"`
	UserRegion       int    `p:"user_region" dc:"用户所在区域"`
	FriendNeedVerify bool   `p:"friend_need_verify" dc:"加好友是否需要验证，默认需要"` //	加好友是否需要验证
}

// 3、修改用户手机号
type UserPhoneModifyReq struct {
	g.Meta `path:"/user-phone-modify" tags:"三、用户信息" method:"post" summary:"3、用户修改手机号"`

	UserId       string `v:"required#请输入uid" p:"user_id" dc:"用户唯一uid"`
	UserPhoneNew string `v:"required#请输入要新手机号" p:"user_phone_new" dc:"用户新手机号"`
	CaptchaCode  string `v:"required#请输入图形验证码" p:"captcha_code" dc:"注册图形验证码"`
	PhoneCode    string `v:"required#请输入手机验证码" p:"phone_code" dc:"注册手机验证码"`
}

// 4、搜索用户请求， 通过id 与 手机号搜索
type SearchUserReq struct {
	g.Meta `path:"/user-search" tags:"三、用户信息" method:"get" summary:"4、搜索用户"`

	SearchInfo string `v:"required" p:"search_info" dc:"用户的的id或手机号"`
}
