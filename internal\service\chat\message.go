/*
******		FileName	:	message.go
******		Describe	:	此文件主要用于聊天消息数据的管理
******		Date		:	2025-05-10
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天 数据保存与获取的实现
 */

package server

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/api/user"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/chat"
	"ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/tools"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// ServerMessage 消息服务结构体
type ServerMessage struct {
	// 分表策略
	shardCount  int8       // 分表数量
	chatManager ServerChat // 聊天长链接
}

// MessageWithReceiver 消息与接收状态的组合结构体（GetMsgListForConvId和GetMsgConvList共用）
type MessageWithReceiver struct {
	// 消息基本信息 (从 ChatMessage 表获取)
	MsgClientId string `gorm:"column:msg_client_id"` //	客户端 消息发送的id
	MsgId       string `gorm:"column:msg_id"`
	MsgType     string `gorm:"column:msg_type"`

	SenderId string      `gorm:"column:sender_id"`
	SendTime *gtime.Time `gorm:"column:send_time"`

	MsgContentFmt  int8   `gorm:"column:msg_content_fmt"`
	MsgContentType string `gorm:"column:msg_content_type"`
	MsgContent     string `gorm:"column:msg_content"`

	// 推送策略信息
	PushStrategy int8   `gorm:"column:push_strategy"`
	PushTargets  string `gorm:"column:push_targets"`

	// 接收状态信息（从 ChatMessageReceiver 表获取）
	ReceiverStatus *int8       `gorm:"column:receiver_status"`
	ReadTime       *gtime.Time `gorm:"column:read_time"`

	// 新增推送策略相关字段
	PushTitle    string `gorm:"column:push_title"`
	PushContent  string `gorm:"column:push_content"`
	PushData     string `gorm:"column:push_data"`
	PushBadge    int    `gorm:"column:push_badge"`
	PushSound    string `gorm:"column:push_sound"`
	PushCategory string `gorm:"column:push_category"`
}

type ConversationWithLastMsg struct {
	// 会话基本信息
	modelChat.ChatConversation

	// 最后一条消息信息（复用MessageWithReceiver的逻辑）
	MessageWithReceiver
}

// 常连接 服务器主动发送给客户端 消息结构体
type WsSendMessage struct {
	MsgType string `json:"msg_type"` // ws 消息类型: chat 聊天消息, system 系统信息, ping 心跳信息

	MsgTargetId   string `json:"msg_target_id" doc:"目标id，群聊就是群会话id， 单聊就是单聊会话id"` // ws 消息接收id， 单聊：对方user_id, 群聊 ：group_id
	MsgTargetType int8   `json:"msg_target_type" doc:"目标类型"`                     // ws 消息接收类型 1 单聊， 2 群聊

	MsgSendId   string `json:"msg_sender_id"`
	MsgSendTime string `json:"msg_sender_time"`

	MsgContentFmt  int8        `json:"msg_content_fmt"`  // ws 消息内容格式 1 json, 2 protobuf
	MsgContentType string      `json:"msg_content_type"` // ws 消息内容类型  text，image，audio，video ,file
	MsgContent     interface{} `json:"msg_content"`      // ws 消息内容 可扩展
}

// GetMsgConvList 1、会话列表-获取
func (s *ServerMessage) GetMsgConvList(req *chat.GetMsgConvListReq, userId string) (res *chat.GetMsgConvListRes, err error) {
	// 1. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100
	}

	// 2. 构建查询条件
	query := dao.Db.Model(&modelChat.ChatConversation{}).
		Where("user_id = ? AND deleted_at IS NULL", userId)

	// 根据会话类型筛选
	if req.ConvType > 0 {
		query = query.Where("conv_type = ?", req.ConvType)
	}

	// 3. 获取会话总数和总未读数（单次查询优化）
	type CountResult struct {
		TotalCount       int64 //	会话总数
		TotalUnreadCount int64 //	未读消息总数
	}
	var countResult CountResult

	// 构建SQL查询
	var sqlQuery string
	var sqlParams []interface{}

	if req.ConvType > 0 {
		// 如果指定了会话类型
		sqlQuery = `
			SELECT
				COUNT(*) as total_count,
				COALESCE(SUM(unread_count), 0) as total_unread_count
			FROM chat_conversations
			WHERE user_id = ? AND deleted_at IS NULL
			AND conv_type = ?`
		sqlParams = []interface{}{userId, req.ConvType}
	} else {
		// 如果没有指定会话类型
		sqlQuery = `
			SELECT
				COUNT(*) as total_count,
				COALESCE(SUM(unread_count), 0) as total_unread_count
			FROM chat_conversations
			WHERE user_id = ? AND deleted_at IS NULL`
		sqlParams = []interface{}{userId}
	}

	// 执行查询
	result := dao.Db.Raw(sqlQuery, sqlParams...).Scan(&countResult)

	if result.Error != nil {
		return nil, result.Error
	}

	// 4. 获取会话列表和最后一条消息内容，同时获取好友关系和群成员信息
	type ConversationWithRelation struct {
		ConversationWithLastMsg
		// 好友关系字段（单聊）
		FriendIsStar      bool `gorm:"column:friend_is_star"`
		FriendIsTop       bool `gorm:"column:friend_is_top"`
		FriendMessageMute bool `gorm:"column:friend_message_mute"`
		// 群成员字段（群聊）
		GroupTop         bool `gorm:"column:group_top"`
		GroupMessageMute bool `gorm:"column:group_message_mute"`
	}
	var conversationsWithMsg []ConversationWithRelation

	// 使用JOIN查询获取会话、最后一条消息和关系信息
	queryBuilder := dao.Db.Table("chat_conversations as cc").
		Select(`
			cc.*,
			cm.msg_client_id as msg_client_id,
			cm.msg_type as msg_type,
			cm.sender_id as sender_id,
			cm.send_time as send_time,
			cm.msg_content_fmt as msg_content_fmt,
			cm.msg_content_type as msg_content_type,
			cm.msg_content as msg_content,
			cmr.status as receiver_status,
			cmr.read_time as read_time,
			fr.is_star as friend_is_star,
			fr.is_top as friend_is_top,
			fr.message_mute as friend_message_mute,
			cgm.group_top as group_top,
			cgm.message_mute as group_message_mute
		`).
		Joins("LEFT JOIN chat_messages as cm ON cc.last_msg_id = cm.msg_id").
		Joins(`LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id
			AND cmr.receiver_id = CASE
				WHEN cm.sender_id = ? THEN cm.receiver_id
				ELSE ?
			END`, userId, userId).
		Joins("LEFT JOIN friend_relations as fr ON cc.conv_type = 1 AND cc.user_id = fr.user_id AND cc.target_id = fr.friend_id AND fr.deleted_at IS NULL").
		Joins("LEFT JOIN chat_group_members as cgm ON cc.conv_type = 2 AND cc.target_id = cgm.group_id AND cc.user_id = cgm.user_id AND cgm.deleted_at IS NULL AND cgm.exit_time IS NULL").
		Where("cc.user_id = ? AND cc.deleted_at IS NULL", userId)

	// 根据会话类型筛选
	if req.ConvType > 0 {
		queryBuilder = queryBuilder.Where("cc.conv_type = ?", req.ConvType)
	}

	// 执行查询 - 修改排序逻辑：优先按置顶排序，然后按最后消息时间倒序
	result = queryBuilder.Order(`
		CASE
			WHEN cc.conv_type = 1 AND fr.is_top = true THEN 1
			WHEN cc.conv_type = 2 AND cgm.group_top = true THEN 1
			ELSE 0
		END DESC,
		cc.last_msg_time DESC
	`).
		Offset((req.Page - 1) * req.Size).
		Limit(req.Size).
		Find(&conversationsWithMsg)

	if result.Error != nil {
		return nil, result.Error
	}

	// 5. 获取会话昵称和头像信息
	// 将 ConversationWithRelation 转换为 ConversationWithLastMsg 以兼容现有函数
	conversationsForInfo := make([]ConversationWithLastMsg, len(conversationsWithMsg))
	for i, conv := range conversationsWithMsg {
		conversationsForInfo[i] = conv.ConversationWithLastMsg
	}
	convInfoMaps := s.getConvNickAndAvatar(conversationsForInfo, userId)

	// 7. 构建响应数据
	convList := make([]chat.MsgConvInfo, 0, len(conversationsWithMsg))

	for _, convLastMsg := range conversationsWithMsg {
		// 构建最后一条消息信息（使用共用函数）
		var lastMsgInfo chat.MsgBasicInfo
		if convLastMsg.LastMsgId != "" {
			lastMsgInfo = s.buildMessageInfo(
				convLastMsg.MsgClientId,
				convLastMsg.LastMsgId,
				convLastMsg.MsgType,
				convLastMsg.SenderId,
				convLastMsg.SendTime,
				convLastMsg.MsgContentFmt,
				convLastMsg.MsgContentType,
				convLastMsg.MsgContent,
				convLastMsg.ReceiverStatus,
				convLastMsg.ReadTime,
				convLastMsg.ConvId,
				convLastMsg.ConvType,
				userId,
				convInfoMaps.UserInfoMap,
				nil, // 会话列表中不需要群聊接收者状态
				nil, // 会话列表中不需要群聊接收者状态
			)
		}

		// 获取会话昵称和头像
		convNick, convAvatar := s.getConvDisplayInfo(convLastMsg.ConvType, convLastMsg.TargetId, convInfoMaps)

		// 1. 根据会话类型获取 IsStar、IsTop、MessageMute 字段
		var isStar, isTop, messageMute bool
		if convLastMsg.ConvType == consts.ReceiverType_Private {
			// 2. 单聊：从好友关系表获取
			isStar = convLastMsg.FriendIsStar
			isTop = convLastMsg.FriendIsTop
			messageMute = convLastMsg.FriendMessageMute
		} else if convLastMsg.ConvType == consts.ReceiverType_Group {
			// 3. 群聊：从群成员表获取（群聊没有星标功能）
			isStar = false // 群聊不支持星标
			isTop = convLastMsg.GroupTop
			messageMute = convLastMsg.GroupMessageMute
		}

		// 4. 构建会话信息
		convInfo := chat.MsgConvInfo{
			ConvId:           convLastMsg.ConvId,
			ConvType:         convLastMsg.ConvType,
			TargetId:         convLastMsg.TargetId,
			UnreadCount:      convLastMsg.UnreadCount,
			ConvNick:         convNick,
			ConvAvatar:       convAvatar,
			IsStar:           isStar,
			IsTop:            isTop,
			MessageMute:      messageMute,
			LastMsgInfo:      lastMsgInfo,
			LastMsgTimestamp: lastMsgInfo.SendTimestamp,
		}

		convList = append(convList, convInfo)
	}

	// 6. 返回结果
	res = &chat.GetMsgConvListRes{
		List:             convList,
		TotalUnreadCount: int(countResult.TotalUnreadCount),
		HasMore:          int(countResult.TotalCount) > (req.Page * req.Size),
	}

	return res, nil
}

// GetMsgListForConvId 2、会话消息获取
func (s *ServerMessage) GetMsgListForConvId(req *chat.GetMsgListForConvIdReq, userId string) (res *chat.GetMsgListForConvIdRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 2. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100 // 默认每页100条消息
	}

	// 3. 解析会话ID，确定会话类型和目标ID
	var convType int8
	var targetId string
	//	验证用户状态
	if strings.Contains(req.ConvId, "-") {
		// 3.1 单聊会话
		convType = consts.ReceiverType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}

		// 验证是否是好友关系
		var friendCount int64 = 0
		result := dao.Db.Unscoped().Model(&modelChat.FriendRelation{}).
			Where("user_id = ? AND friend_id = ?", userId, targetId).
			Count(&friendCount)
		if result.Error != nil {
			return nil, fmt.Errorf("验证好友关系失败: %w", result.Error)
		}
		if friendCount == 0 {
			return nil, errors.New("对方不是您的好友，无法查看消息")
		}
	} else {
		// 3.2 群聊会话
		convType = consts.ReceiverType_Group
		targetId = req.ConvId

		// 检查用户是否是群成员
		var friendCount int64 = 0
		result := dao.Db.Unscoped().Model(&modelChat.ChatGroupMember{}).
			Where("group_id = ? AND user_id = ?", targetId, userId).
			Count(&friendCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证群成员关系失败: %w", result.Error)
		}
		if friendCount == 0 {
			return nil, errors.New("您不是该群组的成员，无法查看消息")
		}
	}

	// 4. 构建查询 - 使用联表查询一次性获取所有需要的数据
	var messagesWithReceiver []MessageWithReceiver
	var queryBuilder *gorm.DB

	if convType == consts.ReceiverType_Private {
		// 4.1 单聊：以 ChatMessageReceiver 表为主，获取用户在该会话中的所有消息
		queryBuilder = dao.Db.Table("chat_message_receivers as cmr").
			Select(`
                cmr.status as receiver_status, 
                cmr.read_time,
                cm.msg_client_id, 
                cm.msg_id, 
                cm.msg_type, 
                cm.sender_id, 
                cm.send_time,
                cm.msg_content_fmt, 
                cm.msg_content_type, 
                cm.msg_content,
                cm.push_strategy, 
                cm.push_targets
            `).
			Joins("INNER JOIN chat_messages cm ON cmr.msg_id = cm.msg_id").
			Where("cmr.receiver_id = ? AND cmr.conv_id = ? AND cm.deleted_at IS NULL AND cmr.deleted_at IS NULL", userId, req.ConvId)
	} else {
		// 4.2 群聊：以 ChatMessageReceiver 表为主，获取用户在群聊中的消息
		queryBuilder = dao.Db.Table("chat_message_receivers as cmr").
			Select(`
                cmr.status as receiver_status, 
                cmr.read_time,
                cm.msg_client_id, 
                cm.msg_id, 
                cm.msg_type, 
                cm.sender_id, 
                cm.send_time,
                cm.msg_content_fmt, 
                cm.msg_content_type, 
                cm.msg_content,
                cm.push_strategy, 
                cm.push_targets
            `).
			Joins("INNER JOIN chat_messages cm ON cmr.msg_id = cm.msg_id").
			Where("cmr.receiver_id = ? AND cmr.conv_id = ? AND cm.deleted_at IS NULL", userId, req.ConvId).
			Where("(cm.push_strategy = 1 OR (cm.push_strategy != 1 AND cm.push_targets LIKE ?))", "%\""+userId+"\"%")
	}

	/*if convType == consts.ReceiverType_Private {
			// 4.1 单聊：查询双方之间的消息（修复接收状态查询逻辑）

			queryBuilder = dao.Db.Table("chat_message_receivers as cmr").
				Select(`
					cmr.status as receiver_status, cmr.read_time,
					cm.msg_client_id, cm.msg_type, cm.sender_id, cm.send_time,
					cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
					cm.push_strategy, cm.push_targets,
				`).
				Joins(`LEFT JOIN chat_messages as cm ON  cmr.msg_id = cm.msg_id`). //	 chat_message_receivers 接收表里面的 消息状态
				Where("cmr.receiver_id = ? AND cmr.conv_id = ? AND cm.deleted_at IS NULL", userId, req.ConvId)
		} else {
			// 4.2 群聊：查询群内的消息
			queryBuilder = dao.Db.Table("chat_messages as cm").
				Select(`
					cm.msg_client_id,cm.msg_id, cm.msg_type, cm.sender_id, cm.send_time,
					cm.msg_content_fmt, cm.msg_content_type, cm.msg_content,
	 				cm.push_strategy, cm.push_targets,
					cmr.status as receiver_status, cmr.read_time
				`).
				Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
				Where("(cm.receiver_id = ? AND cm.receiver_type = ?  AND cm.deleted_at IS NULL) AND (cm.push_strategy = 1 OR (cm.push_strategy != 1 AND cm.push_targets LIKE ?))",
					targetId, convType, "%\""+userId+"\"%")
		}*/

	// 5. 获取消息总数
	var totalCount int64 = 0
	countQuery := queryBuilder.Session(&gorm.Session{})
	result := countQuery.Count(&totalCount)
	if result.Error != nil {
		return nil, fmt.Errorf("获取消息总数失败: %w", result.Error)
	}

	// 6. 获取消息列表
	result = queryBuilder.Order("cm.send_time DESC"). // 按发送时间降序
								Offset((req.Page - 1) * req.Size).
								Limit(req.Size).
								Find(&messagesWithReceiver)

	if result.Error != nil {
		return nil, fmt.Errorf("获取消息列表失败: %w", result.Error)
	}

	// 7. 收集所有发送者ID，用于批量获取用户信息
	senderIds := make([]string, 0, len(messagesWithReceiver))
	senderIdMap := make(map[string]bool)

	for _, msg := range messagesWithReceiver {
		if !senderIdMap[msg.SenderId] {
			senderIds = append(senderIds, msg.SenderId)
			senderIdMap[msg.SenderId] = true
		}
	}

	// 8. 批量获取用户信息
	var userInfos []user.UserInfoRes
	if len(senderIds) > 0 {
		dao.Db.Model(&modelUser.UserInfo{}).
			Select("user_id, user_nick, user_avatar").
			Where("user_id IN ?", senderIds).
			Find(&userInfos)
	}

	// 创建用户信息映射
	userInfoMap := make(map[string]user.UserInfoRes, len(userInfos))
	for _, userIfno := range userInfos {
		userInfoMap[userIfno.UserId] = userIfno
	}

	// 9. 获取群聊消息的接收者状态统计（仅群聊需要）
	var msgReceiversReadMap map[string][]chat.MessageReceiverInfo
	var msgReceiversAllMap map[string][]chat.MessageReceiverInfo
	if convType == consts.ReceiverType_Group {
		msgReceiversReadMap, msgReceiversAllMap = s.getGroupMessageReceiversStatus(messagesWithReceiver, targetId)
	}

	// 10. 构建响应数据
	msgList := make([]chat.MsgBasicInfo, 0, len(messagesWithReceiver))

	for _, msg := range messagesWithReceiver {
		// 获取群聊接收者状态（仅群聊需要）
		var receiversRead []chat.MessageReceiverInfo
		var receiversAll []chat.MessageReceiverInfo
		if convType == consts.ReceiverType_Group && msgReceiversReadMap != nil {
			receiversRead = msgReceiversReadMap[msg.MsgId]
		}
		if convType == consts.ReceiverType_Group && msgReceiversAllMap != nil {
			receiversAll = msgReceiversAllMap[msg.MsgId]
		}

		// 构建消息信息（使用共用函数）
		messageInfo := s.buildMessageInfo(
			msg.MsgClientId,
			msg.MsgId,
			msg.MsgType,
			msg.SenderId,
			msg.SendTime,
			msg.MsgContentFmt,
			msg.MsgContentType,
			msg.MsgContent,
			msg.ReceiverStatus,
			msg.ReadTime,
			req.ConvId,
			convType,
			userId,
			userInfoMap,
			receiversRead,
			receiversAll,
		)

		msgList = append(msgList, messageInfo)
	}

	// 10. 异步更新会话未读数为0
	/*go func() {
		dao.Db.Model(&modelChat.ChatConversation{}).
			Where("user_id = ? AND target_id = ? AND conv_type = ?", userId, targetId, convType).
			Update("unread_count", 0)
	}()*/

	// 11. 返回结果
	res = &chat.GetMsgListForConvIdRes{
		List:    msgList,
		Count:   int(totalCount),
		HasMore: int(totalCount) > (req.Page * req.Size),
	}

	return res, nil
}

// MarkConvMsgRead 3、会话消息-标记已读(部分消息)， 只修改消息接收者的状态，不用修改消息体
//
//	只能标记别人发送的消息为已读, 如果是单聊， 将发送者的消息自己接收的消息也设置为已读
func (s *ServerMessage) MarkConvMsgReadEx(req *chat.MarkConvMsgReadReq, userId string) (res *chat.MarkConvMsgReadRes, err error) {
	// 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}
	if len(req.MsgIds) == 0 {
		return nil, errors.New("消息ID列表不能为空")
	}

	// 解析会话ID，确定会话类型和目标ID
	var convType int8
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 单聊会话
		convType = consts.ReceiverType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}
	} else {
		// 群聊会话
		convType = consts.ReceiverType_Group
		targetId = req.ConvId
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 验证消息是否属于该会话，并且只允许标记别人发送的消息为已读
	var validMsgIds []string
	var query *gorm.DB
	if convType == consts.ReceiverType_Private {
		// 单聊：只允许标记对方发送给自己的消息为已读
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND sender_id = ? AND receiver_id = ? AND deleted_at IS NULL",
				req.MsgIds, targetId, userId,
			)
	} else {
		// 群聊：只允许标记别人发送到群里的消息为已读（排除自己发送的消息）
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND receiver_id = ? AND receiver_type = ? AND sender_id != ? AND deleted_at IS NULL",
				req.MsgIds, targetId, convType, userId,
			)
	}

	result := query.Pluck("msg_id", &validMsgIds)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果没有有效消息，直接返回
	if len(validMsgIds) == 0 {
		tx.Rollback()
		return &chat.MarkConvMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 更新消息接收状态
	now := time.Now()
	readTime := gtime.Now()
	result = tx.Model(&modelChat.ChatMessageReceiver{}).Where(
		"msg_id IN ? AND receiver_id = ? AND status < ?",
		validMsgIds, userId, consts.MessageStatus_Readed,
	).Updates(map[string]interface{}{
		"read_time":  readTime,
		"status":     consts.MessageStatus_Readed,
		"updated_at": now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 获取更新的记录数
	var updatedCount int64 = result.RowsAffected

	// 如果有消息被标记为已读，更新会话的未读计数
	if updatedCount > 0 {
		// 直接更新会话的未读计数，使用SQL的GREATEST函数确保不会出现负数
		// 这样可以避免先查询再更新的两步操作，提高性能
		result = tx.Exec(
			"UPDATE chat_conversations SET unread_count = GREATEST(0, unread_count - ?), updated_at = ? WHERE user_id = ? AND target_id = ? AND conv_type = ?",
			updatedCount, now, userId, targetId, convType,
		)

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	//	获取未读数量
	var conversation modelChat.ChatConversation
	dao.Db.Model(&modelChat.ChatConversation{}).
		Select("unread_count").
		Where("user_id = ? AND ConvId = ?", userId, req.ConvId).
		Find(&conversation)

	// 返回结果
	res = &chat.MarkConvMsgReadRes{
		SuccessCount: int(updatedCount),
		UnreadCount:  conversation.UnreadCount,
	}

	//	发送通知给消息发送者
	if convType == consts.ReceiverType_Private {
		//	单聊信息已读
		go func(_strConvId, _strTargetId string, _validMsgIdSlice []string) {
			s.readMsgPrivateNotice(_strConvId, _strTargetId, _validMsgIdSlice)
		}(req.ConvId, targetId, validMsgIds)
	} else {
		//	群聊信息已读
		go func(_strConvId, _strTargetId string, _nConvType int8, _validMsgIdSlice []string) {
			var validMsgSend []modelChat.ChatMessage
			result = dao.Db.Model(&modelChat.ChatMessage{}).
				Select("sender_id, msg_id").
				Where(
					"msg_id IN ? AND receiver_id = ? AND receiver_type = ? AND deleted_at IS NULL",
					_validMsgIdSlice, _strTargetId, _nConvType,
				).Find(&validMsgSend)
			if len(validMsgSend) > 0 {
				sendMsgMap := make(map[string][]string, len(validMsgSend))
				for i := 0; i < len(validMsgSend); i++ {
					if validMsgSlice, exists := sendMsgMap[validMsgSend[i].SenderId]; exists {
						validMsgSlice = append(validMsgSlice, validMsgSend[i].MsgId)
						sendMsgMap[validMsgSend[i].SenderId] = validMsgSlice
					} else {
						validMsgSlice := make([]string, 0)
						validMsgSlice = append(validMsgSlice, validMsgSend[i].MsgId)
						sendMsgMap[validMsgSend[i].SenderId] = validMsgSlice
					}
				}
				s.readMsgGroupNotice(_strConvId, sendMsgMap)
			}
		}(req.ConvId, targetId, convType, validMsgIds)
	}

	return
}

// MarkConvMsgRead 3、会话消息-标记已读(部分消息)， 只修改消息接收者的状态，不用修改消息体
//
//	只能标记别人发送的消息为已读, 如果是单聊， 将发送者发送给自己的消息状态，一起同步接收者接收这条消息的状态
func (s *ServerMessage) MarkConvMsgRead(req *chat.MarkConvMsgReadReq, userId string) (res *chat.MarkConvMsgReadRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}
	if len(req.MsgIds) == 0 {
		return nil, errors.New("消息ID列表不能为空")
	}

	// 2. 解析会话ID，确定会话类型和目标ID
	var convType int8
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 2.1 单聊会话
		convType = consts.ReceiverType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}
	} else {
		// 2.2 群聊会话
		convType = consts.ReceiverType_Group
		targetId = req.ConvId
	}

	// 3. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 4. 验证消息是否属于该会话，并且只允许标记别人发送的消息为已读
	var validMsgIds []string
	var query *gorm.DB
	if convType == consts.ReceiverType_Private {
		// 4.1 单聊：只允许标记对方发送给自己的消息为已读
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND sender_id = ? AND receiver_id = ? AND deleted_at IS NULL",
				req.MsgIds, targetId, userId,
			)
	} else {
		// 4.2 群聊：只允许标记别人发送到群里的消息为已读（排除自己发送的消息）
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND receiver_id = ? AND receiver_type = ? AND sender_id != ? AND deleted_at IS NULL",
				req.MsgIds, targetId, convType, userId,
			)
	}

	result := query.Pluck("msg_id", &validMsgIds)
	if result.Error != nil {
		return nil, result.Error
	}

	// 5. 如果没有有效消息，直接返回
	if len(validMsgIds) == 0 {
		if err = tx.Commit().Error; err != nil {
			return nil, fmt.Errorf("提交事务失败: %w", err)
		}
		return &chat.MarkConvMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 6. 更新消息接收状态（只修改接收者状态，不修改消息体）
	now := time.Now()
	readTime := gtime.Now()

	// 6.1 更新当前用户作为接收者的消息状态
	result = tx.Model(&modelChat.ChatMessageReceiver{}).Where(
		"msg_id IN ? AND receiver_id = ? AND status < ?",
		validMsgIds, userId, consts.MessageStatus_Readed,
	).Updates(map[string]interface{}{
		"read_time":  readTime,
		"status":     consts.MessageStatus_Readed,
		"updated_at": now,
	})

	if result.Error != nil {
		return nil, fmt.Errorf("更新接收者消息状态失败: %w", result.Error)
	}

	var updatedCount int64 = result.RowsAffected

	// 6.2 单聊时，同步更新发送者发送给自己的消息状态
	if convType == consts.ReceiverType_Private && updatedCount > 0 {
		// 查询发送者发送给自己的相同消息ID
		var senderMsgIds []string
		result = tx.Model(&modelChat.ChatMessage{}).
			Select("msg_id").
			Where(
				"msg_id IN ? AND sender_id = ? AND receiver_id = ? AND deleted_at IS NULL",
				validMsgIds, targetId, userId,
			).Pluck("msg_id", &senderMsgIds)

		if result.Error != nil {
			return nil, fmt.Errorf("查询发送者消息失败: %w", result.Error)
		}

		// 同步更新发送者的消息接收状态
		if len(senderMsgIds) > 0 {
			result = tx.Model(&modelChat.ChatMessageReceiver{}).Where(
				"msg_id IN ? AND receiver_id = ? AND status < ?",
				senderMsgIds, targetId, consts.MessageStatus_Readed,
			).Updates(map[string]interface{}{
				"read_time":  readTime,
				"status":     consts.MessageStatus_Readed,
				"updated_at": now,
			})

			if result.Error != nil {
				return nil, fmt.Errorf("同步发送者消息状态失败: %w", result.Error)
			}
		}
	}

	// 7. 更新会话的未读计数
	if updatedCount > 0 {
		// 使用SQL的GREATEST函数确保不会出现负数，提高性能
		result = tx.Exec(
			"UPDATE chat_conversations SET unread_count = GREATEST(0, unread_count - ?), updated_at = ? WHERE user_id = ? AND target_id = ? AND conv_type = ?",
			updatedCount, now, userId, targetId, convType,
		)

		if result.Error != nil {
			return nil, fmt.Errorf("更新会话未读计数失败: %w", result.Error)
		}
	}

	// 8. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 9. 获取更新后的未读数量
	var conversation modelChat.ChatConversation
	dao.Db.Model(&modelChat.ChatConversation{}).
		Select("unread_count").
		Where("user_id = ? AND conv_id = ?", userId, req.ConvId).
		First(&conversation)

	// 10. 异步发送通知给消息发送者
	if convType == consts.ReceiverType_Private {
		// 10.1 单聊信息已读通知
		go func(_strConvId, _strTargetId string, _validMsgIdSlice []string) {
			s.readMsgPrivateNotice(_strConvId, _strTargetId, _validMsgIdSlice)
		}(req.ConvId, targetId, validMsgIds)
	} else {
		// 10.2 群聊信息已读通知
		go func(_strConvId, _strTargetId string, _nConvType int8, _validMsgIdSlice []string) {
			var validMsgSend []modelChat.ChatMessage
			result = dao.Db.Model(&modelChat.ChatMessage{}).
				Select("sender_id, msg_id").
				Where(
					"msg_id IN ? AND receiver_id = ? AND receiver_type = ? AND deleted_at IS NULL",
					_validMsgIdSlice, _strTargetId, _nConvType,
				).Find(&validMsgSend)
			if len(validMsgSend) > 0 {
				sendMsgMap := make(map[string][]string, len(validMsgSend))
				for i := 0; i < len(validMsgSend); i++ {
					sendMsgMap[validMsgSend[i].SenderId] = append(sendMsgMap[validMsgSend[i].SenderId], validMsgSend[i].MsgId)
				}
				s.readMsgGroupNotice(_strConvId, sendMsgMap)
			}
		}(req.ConvId, targetId, convType, validMsgIds)
	}

	// 11. 返回结果
	return &chat.MarkConvMsgReadRes{
		SuccessCount: int(updatedCount),
		UnreadCount:  conversation.UnreadCount,
	}, nil
}

// MarkConvAllMsgRead 4、会话消息-标记已读(全部)
func (s *ServerMessage) MarkConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	if req.ConvId == "" {
		// 如果会话ID为空，标记所有会话的所有消息为已读
		return s.markAllConvAllMsgRead(req, userId)
	} else {
		// 如果会话ID不为空，标记指定会话的所有消息为已读
		return s.markConvAllMsgRead(req, userId)
	}
}

// markConvAllMsgRead 4.1 标记指定会话所有消息已读
func (s *ServerMessage) markConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 2. 解析会话ID，确定会话类型和目标ID
	var convType int8
	var targetId string

	if strings.Contains(req.ConvId, "-") {
		// 2.1 单聊会话
		convType = consts.ReceiverType_Private
		parts := strings.Split(req.ConvId, "-")
		if len(parts) != 2 {
			return nil, errors.New("无效的会话ID格式")
		}

		// 确定目标ID（对方ID）
		if parts[0] == userId {
			targetId = parts[1]
		} else if parts[1] == userId {
			targetId = parts[0]
		} else {
			return nil, errors.New("您不是该会话的成员")
		}

		// 验证是否是好友关系
		var friendCount int64
		result := dao.Db.Model(&modelChat.FriendRelation{}).
			Where("user_id = ? AND friend_id = ? AND deleted_at IS NULL", userId, targetId).
			Count(&friendCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证好友关系失败: %w", result.Error)
		}

		if friendCount == 0 {
			return nil, errors.New("对方不是您的好友，无法标记消息已读")
		}
	} else {
		// 2.2 群聊会话
		convType = consts.ReceiverType_Group
		targetId = req.ConvId

		// 检查用户是否是群成员
		var memberCount int64
		result := dao.Db.Model(&modelChat.ChatGroupMember{}).
			Where("group_id = ? AND user_id = ? AND deleted_at IS NULL", targetId, userId).
			Count(&memberCount)

		if result.Error != nil {
			return nil, fmt.Errorf("验证群成员关系失败: %w", result.Error)
		}

		if memberCount == 0 {
			return nil, errors.New("您不是该群组的成员，无法标记消息已读")
		}
	}

	// 3. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 4. 查询需要标记为已读的消息ID
	type chatUnreadMsg struct {
		MsgId    string `gorm:"name:msg_id"`    // 全局唯一消息ID，用于跨分片查询
		SenderId string `gorm:"name:sender_id"` //	发现消息的id
	}
	var unreadMsgIds []chatUnreadMsg
	var query *gorm.DB

	if convType == consts.ReceiverType_Private {
		// 4.1 单聊：查询对方发送给自己且未读的消息
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("cm.msg_id,cm.sender_id").
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("cm.sender_id = ? AND cm.receiver_id = ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
				targetId, userId, consts.MessageStatus_Readed)
	} else {
		// 4.2 群聊：查询别人发送到群里且自己未读的消息（排除自己发送的消息）
		query = tx.Model(&modelChat.ChatMessage{}).
			Select("cm.msg_id,cm.sender_id").
			Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
			Where("cm.receiver_id = ? AND cm.receiver_type = ? AND cm.sender_id != ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
				targetId, convType, userId, consts.MessageStatus_Readed)
	}

	result := query.Table("chat_messages as cm").Pluck("cm.msg_id,cm.sender_id", &unreadMsgIds)
	if result.Error != nil {
		return nil, fmt.Errorf("查询未读消息失败: %w", result.Error)
	}

	// 5. 如果没有未读消息，直接返回
	if len(unreadMsgIds) == 0 {
		tx.Commit() // 提交空事务
		return &chat.MarkConvAllMsgReadRes{
			SuccessCount: 0,
			UnreadCount:  0,
		}, nil
	}

	// 6. 更新消息接收状态
	now := time.Now()
	readTime := gtime.Now()
	//	有效ids
	var validMsgIds []string
	sendMsgMap := make(map[string][]string, len(unreadMsgIds))

	for _, msgId := range unreadMsgIds {
		validMsgIds = append(validMsgIds, msgId.MsgId)

		if validMsgSlice, exists := sendMsgMap[msgId.SenderId]; exists {
			validMsgSlice = append(validMsgSlice, msgId.MsgId)
			sendMsgMap[msgId.SenderId] = validMsgSlice
		} else {
			validMsgSlice := make([]string, 0)
			validMsgSlice = append(validMsgSlice, msgId.MsgId)
			sendMsgMap[msgId.SenderId] = validMsgSlice
		}
	}

	// 6.3 更新已有的接收记录
	if len(validMsgIds) > 0 {
		result = tx.Model(&modelChat.ChatMessageReceiver{}).
			Where("msg_id IN ? AND receiver_id = ?", validMsgIds, userId).
			Updates(map[string]interface{}{
				"read_time":  readTime,
				"status":     consts.MessageStatus_Readed,
				"updated_at": now,
			})

		if result.Error != nil {
			return nil, fmt.Errorf("更新消息接收状态失败: %w", result.Error)
		}
	}

	// 7. 更新会话的未读计数为0
	result = tx.Model(&modelChat.ChatConversation{}).
		Where("user_id = ? AND target_id = ? AND conv_type = ?", userId, targetId, convType).
		Updates(map[string]interface{}{
			"unread_count": 0,
			"updated_at":   now,
		})

	if result.Error != nil {
		return nil, fmt.Errorf("更新会话未读计数失败: %w", result.Error)
	}

	// 8. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	//	发送通知给消息发送者
	if convType == consts.ReceiverType_Private {
		//	单聊信息已读
		go func(_strConvId, _strTargetId string, _validMsgIdSlice []string) {
			s.readMsgPrivateNotice(_strConvId, _strTargetId, _validMsgIdSlice)
		}(req.ConvId, targetId, validMsgIds)
	} else {
		//	群聊信息已读
		go func(_strConvId string, _sendMsgMap map[string][]string) {
			if len(_sendMsgMap) > 0 {
				s.readMsgGroupNotice(_strConvId, sendMsgMap)
			}
		}(req.ConvId, sendMsgMap)
	}

	// 9. 返回结果
	return &chat.MarkConvAllMsgReadRes{
		SuccessCount: len(unreadMsgIds),
	}, nil
}

// markAllConvAllMsgRead 4.2 标记所有会话的所有消息为已读
func (s *ServerMessage) markAllConvAllMsgRead(req *chat.MarkConvAllMsgReadReq, userId string) (res *chat.MarkConvAllMsgReadRes, err error) {
	// 1. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	// 2. 获取用户的所有会话
	var conversations []modelChat.ChatConversation
	result := tx.Model(&modelChat.ChatConversation{}).
		Where("user_id = ? AND deleted_at IS NULL AND unread_count > 0", userId).
		Find(&conversations)

	if result.Error != nil {
		return nil, fmt.Errorf("获取用户会话列表失败: %w", result.Error)
	}

	// 3. 如果没有会话，直接返回
	if len(conversations) == 0 {
		tx.Commit() // 提交空事务
		return &chat.MarkConvAllMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 4. 收集所有会话的目标ID和类型，用于后续查询
	type ConvTarget struct {
		TargetId string
		ConvType int8
		ConvId   string
	}

	convTargets := make([]ConvTarget, 0, len(conversations))
	for _, conv := range conversations {
		convTargets = append(convTargets, ConvTarget{
			TargetId: conv.TargetId,
			ConvType: conv.ConvType,
			ConvId:   conv.ConvId,
		})
	}

	// 5. 查询所有未读消息
	var totalUnreadMsgIds []string
	now := time.Now()
	readTime := gtime.Now()
	totalMarkedCount := 0

	// 6. 逐个处理每个会话
	for _, target := range convTargets {
		var unreadMsgIds []string
		var query *gorm.DB

		if target.ConvType == consts.ReceiverType_Private {
			// 6.1 单聊：查询对方发送给自己且未读的消息
			query = tx.Model(&modelChat.ChatMessage{}).
				Select("cm.msg_id").
				Table("chat_messages as cm").
				Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
				Where("cm.sender_id = ? AND cm.receiver_id = ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
					target.TargetId, userId, consts.MessageStatus_Readed)
		} else {
			// 6.2 群聊：查询别人发送到群里且自己未读的消息（排除自己发送的消息）
			query = tx.Model(&modelChat.ChatMessage{}).
				Select("cm.msg_id").
				Table("chat_messages as cm").
				Joins("LEFT JOIN chat_message_receivers as cmr ON cm.msg_id = cmr.msg_id AND cmr.receiver_id = ?", userId).
				Where("cm.receiver_id = ? AND cm.receiver_type = ? AND cm.sender_id != ? AND cm.deleted_at IS NULL AND (cmr.status IS NULL OR cmr.status < ?)",
					target.TargetId, target.ConvType, userId, consts.MessageStatus_Readed)
		}

		result = query.Pluck("cm.msg_id", &unreadMsgIds)
		if result.Error != nil {
			return nil, fmt.Errorf("查询会话 %s 的未读消息失败: %w", target.ConvId, result.Error)
		}

		if len(unreadMsgIds) == 0 {
			continue // 跳过没有未读消息的会话
		}

		totalUnreadMsgIds = append(totalUnreadMsgIds, unreadMsgIds...)

		// 6.3 更新会话的未读计数为0
		result = tx.Model(&modelChat.ChatConversation{}).
			Where("user_id = ? AND target_id = ? AND conv_type = ?", userId, target.TargetId, target.ConvType).
			Updates(map[string]interface{}{
				"unread_count": 0,
				"updated_at":   now,
			})

		if result.Error != nil {
			return nil, fmt.Errorf("更新会话 %s 的未读计数失败: %w", target.ConvId, result.Error)
		}
	}

	// 7. 如果没有未读消息，直接返回
	if len(totalUnreadMsgIds) == 0 {
		tx.Commit() // 提交事务
		return &chat.MarkConvAllMsgReadRes{
			SuccessCount: 0,
		}, nil
	}

	// 8. 查找已有的接收记录
	var existingReceiverIds []string
	result = tx.Model(&modelChat.ChatMessageReceiver{}).
		Where("msg_id IN ? AND receiver_id = ?", totalUnreadMsgIds, userId).
		Pluck("msg_id", &existingReceiverIds)

	if result.Error != nil {
		return nil, fmt.Errorf("查询现有接收记录失败: %w", result.Error)
	}

	// 8.1 创建接收记录ID映射，用于快速查找
	existingMap := make(map[string]bool, len(existingReceiverIds))
	for _, id := range existingReceiverIds {
		existingMap[id] = true
	}

	// 8.2 更新已有的接收记录
	if len(existingReceiverIds) > 0 {
		result = tx.Model(&modelChat.ChatMessageReceiver{}).
			Where("msg_id IN ? AND receiver_id = ?", existingReceiverIds, userId).
			Updates(map[string]interface{}{
				"read_time":  readTime,
				"status":     consts.MessageStatus_Readed,
				"updated_at": now,
			})

		if result.Error != nil {
			return nil, fmt.Errorf("更新消息接收状态失败: %w", result.Error)
		}

		totalMarkedCount += len(existingReceiverIds)
	}

	// 8.3 创建新的接收记录
	// 为每个消息找到对应的会话ID
	msgToConvMap := make(map[string]string)
	for _, target := range convTargets {
		var msgIds []string

		if target.ConvType == consts.ReceiverType_Private {
			// 单聊
			result = tx.Model(&modelChat.ChatMessage{}).
				Where("((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)) AND msg_id IN ? AND deleted_at IS NULL",
					userId, target.TargetId, target.TargetId, userId, totalUnreadMsgIds).
				Pluck("msg_id", &msgIds)
		} else {
			// 群聊
			result = tx.Model(&modelChat.ChatMessage{}).
				Where("receiver_id = ? AND receiver_type = ? AND msg_id IN ? AND deleted_at IS NULL",
					target.TargetId, target.ConvType, totalUnreadMsgIds).
				Pluck("msg_id", &msgIds)
		}

		if result.Error != nil {
			return nil, fmt.Errorf("查询会话 %s 的消息失败: %w", target.ConvId, result.Error)
		}

		for _, msgId := range msgIds {
			msgToConvMap[msgId] = target.ConvId
		}
	}

	// 创建新的接收记录
	var newReceivers []modelChat.ChatMessageReceiver
	for _, msgId := range totalUnreadMsgIds {
		if !existingMap[msgId] {
			convId, exists := msgToConvMap[msgId]
			if !exists {
				// 如果找不到对应的会话ID，跳过
				continue
			}

			newReceivers = append(newReceivers, modelChat.ChatMessageReceiver{
				ConvId:     convId,
				MsgId:      msgId,
				ReceiverId: userId,
				ReadTime:   readTime,
				Status:     consts.MessageStatus_Readed,
				CreatedAt:  &now,
				UpdatedAt:  &now,
			})
		}
	}

	// 批量创建新的接收记录
	if len(newReceivers) > 0 {
		result = tx.Create(&newReceivers)
		if result.Error != nil {
			return nil, fmt.Errorf("创建消息接收记录失败: %w", result.Error)
		}

		totalMarkedCount += len(newReceivers)
	}

	// 9. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 10. 返回结果
	return &chat.MarkConvAllMsgReadRes{
		SuccessCount: totalMarkedCount,
	}, nil
}

// readMsgPrivateNotice 单聊 已读消息通知
func (s *ServerMessage) readMsgPrivateNotice(convId string, targetId string, validMsgIds []string) {
	//	调用长连接 发送已读数据
	readInfo := chat.ReadMsgInfo{}
	readInfo.MsgStatus = consts.MessageStatus_Readed
	readInfo.MsgIds = validMsgIds

	objReadInfo := &WebSocketMessage{
		MsgType:     consts.MsgType_ChatNotice,
		MsgClientId: "",

		MsgReceiverType: consts.ReceiverType_Private,
		MsgReceiverId:   targetId,

		MsgContent:     readInfo,
		MsgContentType: consts.ConntetType_MsgStatusUpdate,
		MsgContentFmt:  1,
	}
	//	不保存数据库, 只发送消息发送者
	_ = s.chatManager.PushServerMsgInfo(objReadInfo, convId, consts.PushStrategy_Receiver, consts.MsgType_System, "")
}

// readMsgGroupNotice 群聊 已读消息通知
func (s *ServerMessage) readMsgGroupNotice(convId string, mapReadMsg map[string][]string) {
	for targetUserId, msgSlice := range mapReadMsg {
		//	调用长连接 发送已读数据
		readInfo := chat.ReadMsgInfo{}
		readInfo.MsgStatus = consts.MessageStatus_Readed
		readInfo.MsgIds = msgSlice

		receiverTargets := make([]string, 0)
		receiverTargets = append(receiverTargets, targetUserId)
		objReadInfo := &WebSocketMessage{
			MsgType:     consts.MsgType_ChatNotice,
			MsgClientId: "",

			MsgReceiverType:    consts.ReceiverType_Group,
			MsgReceiverId:      convId,
			MsgReceiverTargets: receiverTargets,

			MsgContent:     readInfo,
			MsgContentType: consts.ConntetType_MsgStatusUpdate,
			MsgContentFmt:  1,
		}
		//	不保存数据库, 只发送消息发送者
		_ = s.chatManager.PushServerMsgInfo(objReadInfo, convId, consts.PushStrategy_GroupCustom, consts.MsgType_System, "")
	}

}

// GetUnreadCount 5、未读消息数-获取
func (s *ServerMessage) GetUnreadCount(req *chat.GetUnreadCountReq, userId string) (res *chat.GetUnreadCountRes, err error) {
	// 构建查询条件
	query := dao.Db.Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND deleted_at IS NULL AND unread_count > 0",
		userId,
	)

	// 如果指定了会话ID，则只查询该会话
	if req.ConvId != "" {
		query = query.Where("conv_id = ?", req.ConvId)
	}

	// 如果指定了目标类型，则按类型筛选
	if req.ConvType > 0 {
		query = query.Where("conv_type = ?", req.ConvType)
	}

	// 获取未读消息总数
	var totalUnreadCount int64
	result := query.Select("COALESCE(SUM(unread_count), 0)").Scan(&totalUnreadCount)
	if result.Error != nil {
		return nil, result.Error
	}

	// 获取未读消息列表 - 使用全新的DB实例，避免受到前面Select语句的影响
	listQuery := dao.Db.Session(&gorm.Session{}).Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND deleted_at IS NULL AND unread_count > 0",
		userId,
	)

	// 应用相同的过滤条件
	if req.ConvId != "" {
		listQuery = listQuery.Where("conv_id = ?", req.ConvId)
	}
	if req.ConvType > 0 {
		listQuery = listQuery.Where("conv_type = ?", req.ConvType)
	}

	// 执行查询获取会话列表
	var conversations []modelChat.ChatConversation
	result = listQuery.Find(&conversations)
	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	var unreadList []chat.UnreadCountInfo
	for _, conv := range conversations {
		unreadInfo := chat.UnreadCountInfo{
			ConvId:      conv.ConvId,
			ConvType:    conv.ConvType,
			UnreadCount: conv.UnreadCount,
			LastMsgId:   conv.LastMsgId,
			LastMsgTime: tools.GtimeToStringNMDHMS(conv.LastMsgTime),
		}
		unreadList = append(unreadList, unreadInfo)
	}

	// 返回结果
	res = &chat.GetUnreadCountRes{
		List:       unreadList,
		TotalCount: int(totalUnreadCount),
	}

	return
}

// DeleteMsg 6、删除自己会话中的消息
func (s *ServerMessage) DeleteMsg(req *chat.DeleteMsgReq, userId string) (res *chat.DeleteMsgRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}
	if len(req.MsgIds) == 0 {
		return nil, errors.New("消息ID列表不能为空")
	}

	// 2. 验证消息是否存在且属于当前用户会话(不管是自己发送的还是对方发送的, 消息体不要删除)
	var validMsgList []modelChat.ChatMessageReceiver
	result := dao.Db.Model(&modelChat.ChatMessageReceiver{}).
		Where("msg_id IN ? AND receiver_id = ? AND deleted_at IS NULL", req.MsgIds, userId).
		Find(&validMsgList)

	if result.Error != nil {
		return nil, result.Error
	}

	if len(validMsgList) == 0 {
		return nil, errors.New("没有可删除的消息")
	}

	validMsgIds := make([]string, 0, len(validMsgList))
	for _, v := range validMsgList {
		validMsgIds = append(validMsgIds, v.MsgId)
	}
	// 4. 开启事务进行软删除
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	now := time.Now()

	// 5. 只删除当前用户的消息接收记录，不删除消息体
	// 这样对方仍然可以看到消息，只有当前用户看不到
	result = tx.Model(&modelChat.ChatMessageReceiver{}).
		Where("msg_id IN ?  AND conv_id = ? AND  receiver_id = ?", validMsgIds, req.ConvId, userId).
		Update("deleted_at", &now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 6. 提交事务
	tx.Commit()

	// 7. 返回结果
	res = &chat.DeleteMsgRes{
		ConvId: req.ConvId,
		MsgIds: validMsgIds,
	}

	return res, nil
}

// RetractMsg 7、撤回消息
func (s *ServerMessage) RetractMsg(req *chat.RetractMsgReq, userId string) (res *chat.RetractMsgRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}
	if req.MsgId == "" {
		return nil, errors.New("消息ID不能为空")
	}

	// 2. 查询消息是否存在
	var message modelChat.ChatMessage
	result := dao.Db.Model(&modelChat.ChatMessage{}).
		Where("msg_id = ? AND deleted_at IS NULL", req.MsgId).
		First(&message)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("消息不存在")
		}
		return nil, result.Error
	}

	// 3. 验证权限：只有发送者可以撤回消息
	if message.SenderId != userId {
		return nil, errors.New("只能撤回自己发送的消息")
	}

	// 4. 验证时间限制：3分钟内可撤回
	if message.SendTime != nil {
		timeDiff := time.Since(message.SendTime.Time)
		if timeDiff > 3*time.Minute {
			return nil, errors.New("消息发送超过3分钟，无法撤回")
		}
	}

	// 5. 开启事务进行撤回操作
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	now := time.Now()

	// 6. 软删除消息体
	result = tx.Model(&modelChat.ChatMessage{}).
		Where("msg_id = ?", req.MsgId).
		Update("deleted_at", &now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 7. 软删除所有相关的消息接收记录
	result = tx.Model(&modelChat.ChatMessageReceiver{}).
		Where("msg_id = ?", req.MsgId).
		Update("deleted_at", &now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 8. 如果这是会话的最后一条消息，需要更新会话的最后消息信息
	// 查找该会话的上一条消息
	var prevMessage modelChat.ChatMessage
	prevResult := tx.Model(&modelChat.ChatMessage{}).
		Where("(sender_id = ? OR receiver_id = ?) AND msg_id != ? AND deleted_at IS NULL",
			userId, message.ReceiverId, req.MsgId).
		Order("send_time DESC").
		First(&prevMessage)

	// 9. 更新相关会话的最后消息信息
	if message.ReceiverType == consts.ReceiverType_Private {
		// 单聊：更新双方的会话
		updateConvs := []struct {
			userId   string
			targetId string
		}{
			{userId, message.ReceiverId},
			{message.ReceiverId, userId},
		}

		for _, conv := range updateConvs {
			updateData := map[string]interface{}{
				"updated_at": &now,
			}

			if prevResult.Error == nil {
				// 有上一条消息，更新为上一条消息
				updateData["last_msg_id"] = prevMessage.MsgId
				updateData["last_msg_time"] = prevMessage.SendTime
			} else {
				// 没有上一条消息，清空最后消息信息
				updateData["last_msg_id"] = ""
				updateData["last_msg_time"] = nil
			}

			tx.Model(&modelChat.ChatConversation{}).
				Where("user_id = ? AND target_id = ? AND conv_type = ?",
					conv.userId, conv.targetId, consts.ReceiverType_Private).
				Updates(updateData)
		}
	} else if message.ReceiverType == consts.ReceiverType_Group {
		// 群聊：更新所有成员的会话
		updateData := map[string]interface{}{
			"updated_at": &now,
		}

		if prevResult.Error == nil {
			updateData["last_msg_id"] = prevMessage.MsgId
			updateData["last_msg_time"] = prevMessage.SendTime
		} else {
			updateData["last_msg_id"] = ""
			updateData["last_msg_time"] = nil
		}

		tx.Model(&modelChat.ChatConversation{}).
			Where("target_id = ? AND conv_type = ?",
				message.ReceiverId, consts.ReceiverType_Group).
			Updates(updateData)
	}

	// 10. 提交事务
	tx.Commit()

	// 11. 返回结果
	res = &chat.RetractMsgRes{
		ConvId: req.ConvId,
		MsgId:  req.MsgId,
	}

	return res, nil
}

// DeleteConv 8、删除会话
func (s *ServerMessage) DeleteConv(req *chat.DeleteConvReq, userId string) (res *chat.DeleteConvRes, err error) {
	// 1. 参数校验
	if req.ConvId == "" {
		return nil, errors.New("会话ID不能为空")
	}

	// 2. 查询会话是否存在
	var conversation modelChat.ChatConversation
	result := dao.Db.Model(&modelChat.ChatConversation{}).
		Where("conv_id = ? AND user_id = ? AND deleted_at IS NULL", req.ConvId, userId).
		First(&conversation)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("会话不存在")
		}
		return nil, result.Error
	}

	// 3. 开启事务进行删除操作
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	now := time.Now()

	// 4. 软删除会话
	result = tx.Model(&modelChat.ChatConversation{}).
		Where("conv_id = ? AND user_id = ?", req.ConvId, userId).
		Update("deleted_at", &now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 5. 软删除当前用户相关的消息接收记录 （只影响当前用户, 自己发送的消息，自己也会收到）
	result = tx.Model(&modelChat.ChatMessageReceiver{}).
		Where("receiver_id = ? AND conv_id = ?", userId, req.ConvId).
		Update("deleted_at", &now)

	// 6. 提交事务
	tx.Commit()

	// 7. 返回结果
	res = &chat.DeleteConvRes{
		ConvId: req.ConvId,
	}

	return res, nil
}

// 更新消息体状态
func (s *ServerMessage) updateMessageStatus(msgId string, receiverId string, receiverType int8, status int) {
	if receiverType == consts.ReceiverType_Private {
		// 更新消息接收状态,单聊，发送者状态也更新
		dao.Db.Model(&modelChat.ChatMessageReceiver{}).Where(
			"msg_id = ? ", msgId,
		).Update("status", status)
	} else {
		// 更新消息接收状态
		dao.Db.Model(&modelChat.ChatMessageReceiver{}).Where(
			"msg_id = ? AND receiver_id = ?",
			msgId, receiverId,
		).Update("status", status)
	}

}

// 计算分片键 - 用于分表策略
func (s *ServerMessage) calculateShardKey(senderId string, receiverId string) int8 {
	// 使用接收者ID的哈希值作为分片键
	// 如果是群聊，直接使用群ID
	// 如果是单聊，使用两个用户ID的组合
	var key string
	if receiverId != "" {
		key = receiverId
	} else {
		key = senderId
	}

	// 简单的哈希算法，将字符串转为整数后对分片数取模
	hashValue := int8(0)
	for _, c := range key {
		hashValue = hashValue*31 + int8(c)
	}

	// 确保返回正数
	if hashValue < 0 {
		hashValue = -hashValue
	}

	return hashValue % s.shardCount
}

// 生成会话ID(单聊 与 群聊, 客户端也与客户端生成的会话id 一致)
func (s *ServerMessage) generateConvId(convType int8, userId string, targetId string) string {
	if convType == consts.ReceiverType_Private {
		// 单聊：确保两个用户ID按字典序排序，保证生成的会话ID唯一
		if userId < targetId {
			return userId + "-" + targetId
		} else {
			return targetId + "-" + userId
		}
	} else {
		// 群聊：直接使用群ID作为会话ID
		return targetId
	}
}

// 保存消息体到数据库
func (s *ServerMessage) saveMsgToDatabase(msg *MessageItem) (msgRet *modelChat.ChatMessage) {
	// 计算分片键
	shardKey := s.calculateShardKey(msg.SenderId, msg.ReceiverId)

	// 创建消息记录
	now := time.Now()
	sendTime := msg.SendTime
	if sendTime == nil {
		sendTime = gtime.Now()
	}

	// 将消息内容转换为字符串
	var msgContentStr string
	if contentBytes, err := json.Marshal(msg.MsgContent); msg.MsgContent != nil && err == nil {
		msgContentStr = string(contentBytes)
	} else {
		msgContentStr = ""
	}
	// 将扩展消息内容转换为字符串
	var msgExtraStr string
	if contentBytes, err := json.Marshal(msg.Extra); msg.Extra != nil && err == nil {
		msgExtraStr = string(contentBytes)
	} else {
		msgExtraStr = ""
	}
	// 将推送推送目标转字符串
	var pushTargetsStr string
	if contentBytes, err := json.Marshal(msg.PushTargets); msg.PushTargets != nil && err == nil {
		pushTargetsStr = string(contentBytes)
	} else {
		pushTargetsStr = ""
	}
	// 1、创建消息记录
	message := &modelChat.ChatMessage{
		MsgType:        msg.MsgType,
		MsgId:          msg.MsgId,
		MsgClientId:    msg.MsgClientId,
		Status:         consts.MessageStatus_Sended, // 已发送
		SenderId:       msg.SenderId,
		SendTime:       sendTime,
		ReceiverId:     msg.ReceiverId,
		ReceiverType:   msg.ReceiverType, // 1表示单聊
		PushStrategy:   msg.PushStrategy,
		PushTargets:    pushTargetsStr,
		MsgContentFmt:  msg.MsgContentFmt,
		MsgContentType: msg.MsgContentType,
		MsgContent:     msgContentStr,
		Extra:          msgExtraStr,

		ShardKey:  shardKey,
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	// 保存消息
	if message.MsgId != "" {
		dao.Db.Create(message)
	}

	return message
}

// 保存单聊消息到数据库
func (s *ServerMessage) savePrivateMessageToDatabase(msg *MessageItem) {
	//	通知消息的不需要报错数据库
	if msg.MsgType == consts.MsgType_ChatNotice {
		return
	}
	// 1、将消息体保存到数据库
	msgModel := s.saveMsgToDatabase(msg)

	// 创建消息接收记录
	receiver := &modelChat.ChatMessageReceiver{
		ConvId:     msg.ConvId,
		MsgId:      msg.MsgId,
		ReceiverId: msg.ReceiverId,
		Status:     consts.MessageStatus_Sended, // 对应接收者
		ShardKey:   s.calculateShardKey("", msg.ReceiverId),
		CreatedAt:  msgModel.CreatedAt,
		UpdatedAt:  msgModel.UpdatedAt,
	}

	// 2、保存接收者的消息记录
	result := dao.Db.Create(receiver)
	if result.Error != nil {
		g.Log().Errorf(context.Background(), "保存接收者的消息记录失败 :%v", result)
	}
	// 3、保存发送者的消息记录
	if msg.SenderId != "system" {
		receiverSend := &modelChat.ChatMessageReceiver{
			ConvId:     msg.ConvId,
			MsgId:      msg.MsgId,
			ReceiverId: msg.SenderId,
			Status:     consts.MessageStatus_Sended, // 对应接收者
			ShardKey:   s.calculateShardKey(msg.SenderId, msg.SenderId),
			CreatedAt:  msgModel.CreatedAt,
			UpdatedAt:  msgModel.UpdatedAt,
		}
		result = dao.Db.Model(&modelChat.ChatMessageReceiver{}).Create(receiverSend)
	}

	// 4、更新或创建会话（修复未读数逻辑）
	s.updateConversation(consts.ReceiverType_Private, msg.SenderId, msg.ReceiverId, msg.MsgId, msgModel.SendTime, msg.SenderId)
	s.updateConversation(consts.ReceiverType_Private, msg.ReceiverId, msg.SenderId, msg.MsgId, msgModel.SendTime, msg.SenderId)
}

// 保存群聊消息到数据库
func (s *ServerMessage) saveGroupMessageToDatabase(msg *MessageItem, members []string) {
	//	通知消息的不需要报错数据库
	if msg.MsgType == consts.MsgType_ChatNotice {
		return
	}
	// 1、将消息体保存到数据库
	msgModel := s.saveMsgToDatabase(msg)

	// 2、为每个成员创建消息接收记录
	for _, memberUserId := range members {
		//	过滤掉自己
		if memberUserId != msg.SenderId {
			//	这个结构体必须放在里面，否则只会创建一条记录
			receiver := &modelChat.ChatMessageReceiver{
				ConvId:     msg.ConvId,
				MsgId:      msg.MsgId,
				ReceiverId: memberUserId,
				Status:     msgModel.Status, // 已发送
				ShardKey:   s.calculateShardKey(msg.SenderId, memberUserId),
				CreatedAt:  msgModel.CreatedAt,
				UpdatedAt:  msgModel.CreatedAt,
			}

			// 2.1、保存接收记录
			dao.Db.Model(&modelChat.ChatMessageReceiver{}).Create(receiver)

			// 2.2、更新成员的会话（修复未读数逻辑）
			s.updateConversation(consts.ReceiverType_Group, memberUserId, msg.ReceiverId, msg.MsgId, msgModel.SendTime, msg.SenderId)
		}

	}

	//	创建自己的接收消息
	if msg.SenderId != "system" {
		receiverSend := &modelChat.ChatMessageReceiver{
			ConvId:     msg.ConvId,
			MsgId:      msg.MsgId,
			ReceiverId: msg.SenderId,
			Status:     msgModel.Status, // 已发送
			ShardKey:   s.calculateShardKey(msg.SenderId, msg.SenderId),
			CreatedAt:  msgModel.CreatedAt,
			UpdatedAt:  msgModel.CreatedAt,
		}

		// 2.1、保存接收记录
		dao.Db.Model(&modelChat.ChatMessageReceiver{}).Create(receiverSend)
		// 3、单独更新发送者的会话（不增加未读数）
		s.updateConversation(consts.ReceiverType_Group, msg.SenderId, msg.ReceiverId, msg.MsgId, msgModel.SendTime, msg.SenderId)
	}

}

// 更新会话
func (s *ServerMessage) updateConversation(ConvType int8, convUserId string, targetId string, msgId string, msgTime *gtime.Time, msgSenderId string) {
	//	系统不需要存储会话列表
	if targetId == consts.MsgType_System {
		return
	}

	// 生成会话ID
	convId := s.generateConvId(ConvType, convUserId, targetId)

	// 查找现有会话
	var conversation modelChat.ChatConversation
	result := dao.Db.Unscoped().Model(&modelChat.ChatConversation{}).Where(
		"user_id = ? AND target_id = ? AND conv_type = ?",
		convUserId, targetId, ConvType,
	).First(&conversation)

	now := time.Now()

	// 如果会话不存在，创建新会话
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 创建新会话
			newConv := &modelChat.ChatConversation{
				ConvId:      convId,
				ConvType:    ConvType,
				UserId:      convUserId,
				TargetId:    targetId,
				LastMsgId:   msgId,
				LastMsgTime: msgTime,
				UnreadCount: 0,
				CreatedAt:   &now,
				UpdatedAt:   &now,
			}

			// 如果消息不是会话所属用户发送的，设置未读数为1
			if msgSenderId != convUserId {
				newConv.UnreadCount = 1
			}

			// 保存新会话
			result = dao.Db.Create(newConv)
			if result.Error != nil {
				g.Log().Errorf(context.Background(), "创建会话失败 :%v", result)
			}
		}
	} else {
		// 更新现有会话
		updateData := map[string]interface{}{
			"last_msg_id":   msgId,
			"last_msg_time": msgTime,
			"updated_at":    &now,
			"deleted_at":    nil,
		}

		// 如果消息不是会话所属用户发送的，增加未读数
		if msgSenderId != convUserId {
			updateData["unread_count"] = gorm.Expr("unread_count + 1")
		}

		// 更新会话
		dao.Db.Model(&conversation).Updates(updateData)
	}
}

// 获取用户基本信息
func (s *ServerMessage) getUserInfo(userId string) (userName string, userAvatar string) {
	// 这里应该调用用户服务获取用户信息
	// 为简化实现，这里直接从数据库查询
	var userInfo struct {
		UserName   string
		UserAvatar string
	}

	// 查询用户信息
	dao.Db.Table("user_info").Select("user_name, user_avatar").Where("user_id = ?", userId).Scan(&userInfo)

	return userInfo.UserName, userInfo.UserAvatar
}

// ConvInfoMaps 会话信息映射结构体
type ConvInfoMaps struct {
	UserInfoMap     map[string]user.UserInfoRes
	GroupInfoMap    map[string]GroupWithMemberInfo
	FriendRemarkMap map[string]string // 好友备注映射，key为好友ID，value为备注
}

// GroupWithMemberInfo 群组信息和成员备注
type GroupWithMemberInfo struct {
	GroupId     string `gorm:"column:group_id"`
	GroupName   string `gorm:"column:group_name"`
	GroupAvatar string `gorm:"column:group_avatar"`
	Remarks     string `gorm:"column:remarks"`
}

// getConvNickAndAvatar 获取会话昵称和头像信息
func (s *ServerMessage) getConvNickAndAvatar(conversations []ConversationWithLastMsg, userId string) *ConvInfoMaps {
	// 1. 收集所有需要查询的ID
	senderIds := make([]string, 0, len(conversations))
	targetUserIds := make([]string, 0, len(conversations))
	groupIds := make([]string, 0, len(conversations))

	senderIdMap := make(map[string]bool)
	targetUserIdMap := make(map[string]bool)
	groupIdMap := make(map[string]bool)

	for _, conv := range conversations {
		// 收集最后一条消息的发送者ID
		if conv.SenderId != "" && !senderIdMap[conv.SenderId] {
			senderIds = append(senderIds, conv.SenderId)
			senderIdMap[conv.SenderId] = true
		}

		// 收集会话相关的ID
		if conv.ConvType == consts.ReceiverType_Private {
			// 单聊：收集目标用户ID
			if !targetUserIdMap[conv.TargetId] {
				targetUserIds = append(targetUserIds, conv.TargetId)
				targetUserIdMap[conv.TargetId] = true
			}
		} else if conv.ConvType == consts.ReceiverType_Group {
			// 群聊：收集群组ID
			if !groupIdMap[conv.TargetId] {
				groupIds = append(groupIds, conv.TargetId)
				groupIdMap[conv.TargetId] = true
			}
		}
	}

	// 2. 批量获取用户信息
	allUserIds := make([]string, 0, len(senderIds)+len(targetUserIds))
	allUserIds = append(allUserIds, senderIds...)
	allUserIds = append(allUserIds, targetUserIds...)

	// 去重
	uniqueUserIds := make([]string, 0, len(allUserIds))
	uniqueUserIdMap := make(map[string]bool)
	for _, id := range allUserIds {
		if !uniqueUserIdMap[id] {
			uniqueUserIds = append(uniqueUserIds, id)
			uniqueUserIdMap[id] = true
		}
	}

	var userInfos []user.UserInfoRes
	if len(uniqueUserIds) > 0 {
		dao.Db.Model(&modelUser.UserInfo{}).
			Select("user_id, user_nick, user_avatar").
			Where("user_id IN ?", uniqueUserIds).
			Find(&userInfos)
	}

	// 创建用户信息映射
	userInfoMap := make(map[string]user.UserInfoRes, len(userInfos))
	for _, userInfo := range userInfos {
		userInfoMap[userInfo.UserId] = userInfo
	}

	// 3. 批量获取好友备注信息（仅针对单聊的目标用户）
	type FriendRemarkInfo struct {
		FriendId string `gorm:"column:friend_id"`
		Remark   string `gorm:"column:remark"`
	}

	var friendRemarks []FriendRemarkInfo
	friendRemarkMap := make(map[string]string)

	if len(targetUserIds) > 0 {
		dao.Db.Model(&modelChat.FriendRelation{}).
			Select("friend_id, remark").
			Where("user_id = ? AND friend_id IN ? AND deleted_at IS NULL AND remark != ''", userId, targetUserIds).
			Find(&friendRemarks)

		// 创建好友备注映射
		for _, remark := range friendRemarks {
			friendRemarkMap[remark.FriendId] = remark.Remark
		}
	}

	// 3. 批量获取群组信息和用户在群组中的备注
	var groupInfos []GroupWithMemberInfo
	if len(groupIds) > 0 {
		dao.Db.Table("chat_group_infos as g").
			Select("g.group_id, g.group_name, g.group_avatar, m.remarks").
			Joins("LEFT JOIN chat_group_members as m ON g.group_id = m.group_id AND m.user_id = ? AND m.deleted_at IS NULL", userId).
			Where("g.group_id IN ? AND g.deleted_at IS NULL", groupIds).
			Find(&groupInfos)
	}

	// 创建群组信息映射
	groupInfoMap := make(map[string]GroupWithMemberInfo, len(groupInfos))
	for _, group := range groupInfos {
		groupInfoMap[group.GroupId] = group
	}

	return &ConvInfoMaps{
		UserInfoMap:     userInfoMap,
		GroupInfoMap:    groupInfoMap,
		FriendRemarkMap: friendRemarkMap,
	}
}

// getConvDisplayInfo 获取会话显示信息（昵称和头像）
func (s *ServerMessage) getConvDisplayInfo(convType int8, targetId string, convInfoMaps *ConvInfoMaps) (string, string) {
	var convNick, convAvatar string

	if convType == consts.ReceiverType_Private {
		// 单聊：获取好友的昵称和头像，优先使用好友备注
		if targetUser, exists := convInfoMaps.UserInfoMap[targetId]; exists {
			// 优先使用好友备注，如果没有备注则使用用户昵称
			if friendRemark, hasRemark := convInfoMaps.FriendRemarkMap[targetId]; hasRemark && friendRemark != "" {
				convNick = friendRemark
			} else {
				convNick = targetUser.UserNick
			}
			convAvatar = targetUser.UserAvatar
		}
	} else if convType == consts.ReceiverType_Group {
		// 群聊：获取群名称和头像，优先使用用户备注
		if groupInfo, exists := convInfoMaps.GroupInfoMap[targetId]; exists {
			// 如果用户有群备注，使用备注作为昵称，否则使用群名称
			if groupInfo.Remarks != "" {
				convNick = groupInfo.Remarks
			} else {
				convNick = groupInfo.GroupName
			}
			convAvatar = groupInfo.GroupAvatar
		}
	}

	return convNick, convAvatar
}

// calculateMessageStatus 计算消息状态（统一从ChatMessageReceiver表获取）
func (s *ServerMessage) calculateMessageStatus(senderId, currentUserId string, receiverStatus *int8) int8 {
	// 1. 如果是自己发送的消息
	if senderId == currentUserId {
		// 自己发送的消息，需要查询对方的接收状态
		// 如果没有接收记录，说明对方还未接收，状态为"已发送"
		if receiverStatus == nil {
			return consts.MessageStatus_Sended // 已发送，对方未接收
		}
		return *receiverStatus // 返回对方的接收状态
	}

	// 2. 如果是接收的消息
	// 如果没有自己的接收记录，说明还未读，状态为"未读"
	if receiverStatus == nil {
		return consts.MessageStatus_Sended // 已发送，自己未读
	}
	return *receiverStatus // 返回自己的接收状态
}

// getGroupMessageReceiversStatus 获取群聊消息的所有接收者状态
func (s *ServerMessage) getGroupMessageReceiversStatus(messages []MessageWithReceiver, groupId string) (map[string][]chat.MessageReceiverInfo, map[string][]chat.MessageReceiverInfo) {
	if len(messages) == 0 {
		return nil, nil
	}

	// 1. 收集所有消息ID
	msgIds := make([]string, 0, len(messages))
	for _, msg := range messages {
		msgIds = append(msgIds, msg.MsgId)
	}

	// 2. 批量查询所有消息的接收者状态
	type ReceiverStatusInfo struct {
		MsgId      string      `gorm:"column:msg_id"`
		ReceiverId string      `gorm:"column:receiver_id"`
		Status     int8        `gorm:"column:status"`
		ReadTime   *gtime.Time `gorm:"column:read_time"`
		UserNick   string      `gorm:"column:user_nick"`
		UserAvatar string      `gorm:"column:user_avatar"`
	}

	var receiversStatus []ReceiverStatusInfo
	dao.Db.Table("chat_message_receivers as cmr").
		Select("cmr.msg_id, cmr.receiver_id, cmr.status, cmr.read_time, ui.user_nick, ui.user_avatar").
		Joins("LEFT JOIN user_infos as ui ON cmr.receiver_id = ui.user_id").
		Where("cmr.msg_id IN ?", msgIds).
		Find(&receiversStatus)

	// 3. 获取群成员列表（用于补充没有接收记录的成员）
	var groupMembers []string
	dao.Db.Model(&modelChat.ChatGroupMember{}).
		Select("user_id").
		Where("group_id = ? AND deleted_at IS NULL AND exit_time IS NULL", groupId).
		Pluck("user_id", &groupMembers)

	// 4. 构建结果映射
	resultRead := make(map[string][]chat.MessageReceiverInfo)
	resultAll := make(map[string][]chat.MessageReceiverInfo)
	// 按消息ID分组
	receiversByMsg := make(map[string][]ReceiverStatusInfo)
	for _, receiver := range receiversStatus {
		receiversByMsg[receiver.MsgId] = append(receiversByMsg[receiver.MsgId], receiver)
	}

	// 5. 为每条消息构建完整的接收者状态列表
	for _, msg := range messages {
		receiversRead := make([]chat.MessageReceiverInfo, 0, len(groupMembers))
		receiversAll := make([]chat.MessageReceiverInfo, 0, len(groupMembers))
		// 创建已有接收记录的映射
		existingReceivers := make(map[string]ReceiverStatusInfo)
		if msgReceivers, exists := receiversByMsg[msg.MsgId]; exists {
			for _, receiver := range msgReceivers {
				existingReceivers[receiver.ReceiverId] = receiver
			}
		}

		// 为每个群成员构建接收状态
		for _, memberId := range groupMembers {
			var receiverInfo chat.MessageReceiverInfo
			if receiver, hasRecord := existingReceivers[memberId]; hasRecord {
				// 有接收记录
				receiverInfo = chat.MessageReceiverInfo{
					ReceiverId:     receiver.ReceiverId,
					ReceiverNick:   receiver.UserNick,
					ReceiverAvatar: receiver.UserAvatar,
					Status:         receiver.Status,
				}
				if receiver.Status == consts.MessageStatus_Readed {
					receiversRead = append(receiversRead, receiverInfo)
				}
				receiversAll = append(receiversAll, receiverInfo)
			}
		}
		resultRead[msg.MsgId] = receiversRead
		resultAll[msg.MsgId] = receiversAll
	}

	return resultRead, resultAll
}

// buildMessageInfo 构建MessageInfo对象（GetMsgListForConvId和GetMsgConvList共用）
func (s *ServerMessage) buildMessageInfo(
	msgClientId, msgId, msgType, senderId string, sendTime *gtime.Time,
	msgContentFmt int8, msgContentType, msgContent string,
	receiverStatus *int8, readTime *gtime.Time,
	convId string, convType int8,
	userId string,
	userInfoMap map[string]user.UserInfoRes,
	receiversRead []chat.MessageReceiverInfo,
	receiversAll []chat.MessageReceiverInfo,
) chat.MsgBasicInfo {
	// 1. 解析消息内容
	var parsedContent interface{}
	if msgContent != "" {
		if err := json.Unmarshal([]byte(msgContent), &parsedContent); err != nil {
			// 如果解析失败，使用原始字符串
			parsedContent = msgContent
		}
	}

	// 1. 获取发送者信息
	var senderNick, senderAvatar string
	if userInfo, exists := userInfoMap[senderId]; exists {
		senderNick = userInfo.UserNick
		senderAvatar = userInfo.UserAvatar
	}

	// 2. 计算消息状态
	msgStatus := s.calculateMessageStatus(senderId, userId, receiverStatus)

	// 3. 转换发送时间戳（确保与字符串时间一致）
	sendTimestamp := tools.GtimeToTimestamp(sendTime)

	// 4. 构建 聊天消息 对象
	chatMsgInfo := chat.ChatMsgInfo{
		MsgId:       msgId,
		MsgClientId: msgClientId,

		MsgInfo: parsedContent,

		Status:        msgStatus,
		StatusTips:    consts.GetMsgStatusTips(msgStatus),
		ReadTimestamp: tools.GtimeToTimestamp(readTime),

		ReceiversRead: receiversRead, // 群聊接收者状态列表（单聊时为空）
		ReceiversAll:  receiversAll,  // 群聊接收者状态列表（单聊时为空）
	}
	// 4.1 设置是否是自己发送的消息
	if senderId == userId {
		chatMsgInfo.IsMyself = true
	}
	//	构建消息基本对象
	messageInfo := chat.MsgBasicInfo{
		MsgType: msgType,

		SenderId:      senderId,
		SenderNick:    senderNick,
		SenderAvatar:  senderAvatar,
		SendTimestamp: sendTimestamp,

		MsgContentFmt:  msgContentFmt,
		MsgContentType: msgContentType,
		MsgContent:     chatMsgInfo,

		ConvId:   convId,
		ConvType: convType,
	}

	return messageInfo
}
