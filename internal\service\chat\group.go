/*
******		FileName	:	group.go
******		Describe	:	此文件主要用于群聊的数据管理
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   群聊管理
 */

package server

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/chat"
	"ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/tools"
	"ayj_chat_back/internal/public/uniqueId"
	server "ayj_chat_back/internal/service/user"
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
	"strings"
	"time"
)

// ServerGroup 群聊管理服务结构体
type ServerGroup struct {
	chatManager ServerChat
}

// CreateGroup 1、创建群聊 使用map去重成员ID;批量插入成员记录,减少时间对象创建
func (s *ServerGroup) CreateGroup(ctx context.Context, req *chat.CreateGroupReq, userId string) (res *chat.CreateGroupRes, err error) {
	// 1. 参数校验
	if req.GroupName == "" {
		return nil, errors.New("群组名称不能为空")
	}
	// 2. 设置默认值
	if req.GroupType <= 0 {
		req.GroupType = 1 // 默认普通群聊
	}
	if req.GroupMaxMembers <= 0 {
		req.GroupMaxMembers = 200 // 默认最大200人
	}
	if req.JoinVerifyType <= 0 {
		req.JoinVerifyType = 2 // 默认需要验证
	}

	// 4. 生成群ID
	groupId := uniqueId.GenerateGroupID()

	// 5. 计算实际成员数量（排除重复的成员ID和群主自己）
	// 性能优化: 预分配map容量，减少扩容次数
	uniqueMembers := make(map[string]bool, len(req.MemberIds))
	for _, memberId := range req.MemberIds {
		// 排除群主自己
		if memberId != userId {
			uniqueMembers[memberId] = true
		}
	}
	if len(uniqueMembers) < 2 {
		return nil, errors.New("群成员不能少于2个人")
	}
	// 实际成员数量 = 唯一成员数量 + 1（群主）
	actualMemberCount := len(uniqueMembers) + 1

	// 6. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 7. 创建群组信息
	now := gtime.Now()

	groupInfo := &modelChat.ChatGroupInfo{
		GroupId:         groupId,
		GroupName:       req.GroupName,
		GroupAvatar:     req.GroupAvatar,
		GroupDesc:       req.GroupDesc,
		GroupOwnerId:    userId, // 创建者为群主
		GroupType:       req.GroupType,
		GroupMaxMembers: req.GroupMaxMembers,
		GroupCurMembers: actualMemberCount, // 使用计算后的实际成员数量
		JoinVerifyType:  req.JoinVerifyType,
		GroupNotice:     req.GroupNotice,
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	// 8. 保存群组信息
	result := tx.Create(groupInfo)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 9. 添加群主为成员
	ownerMember := &modelChat.ChatGroupMember{
		GroupId:     groupId,
		UserId:      userId,
		MemberRole:  consts.ChatGroupRole_Owner, // 群主角色
		MemberNick:  "",                         // 默认使用用户昵称
		JoinTime:    now,
		InviterId:   "", // 群主没有邀请人
		MessageMute: false,
		GroupTop:    false,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	result = tx.Create(ownerMember)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 10. 添加其他成员 性能优化: 批量创建成员记录，减少数据库交互次数
	if len(uniqueMembers) > 0 {
		members := make([]modelChat.ChatGroupMember, 0, len(uniqueMembers))

		for memberId := range uniqueMembers {
			members = append(members, modelChat.ChatGroupMember{
				GroupId:     groupId,
				UserId:      memberId,
				MemberRole:  consts.ChatGroupRole_Comm, // 普通成员
				MemberNick:  "",                        // 默认使用用户昵称
				JoinTime:    now,
				InviterId:   userId, // 邀请人为群主
				MessageMute: false,
				GroupTop:    false,
				CreatedAt:   now,
				UpdatedAt:   now,
			})
		}

		// 批量插入成员记录
		result = tx.Create(&members)
		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 11. 提交事务
	tx.Commit()

	// 12. 构建返回结果
	res = &chat.CreateGroupRes{
		GroupBasicInfo: chat.GroupBasicInfo{
			GroupId:         groupId,
			GroupName:       req.GroupName,
			GroupAvatar:     req.GroupAvatar,
			GroupDesc:       req.GroupDesc,
			GroupOwnerId:    userId,
			GroupType:       req.GroupType,
			GroupMaxMembers: req.GroupMaxMembers,
			GroupCurMembers: actualMemberCount,
			JoinVerifyType:  req.JoinVerifyType,
			GroupNotice:     req.GroupNotice,
			CreatedAt:       tools.GtimeToStringNMDHMS(now),
		},
	}
	userNick := tools.GetUserNickFromCtx(ctx)
	go func(_strUserNick string) {
		//	推送实时消息
		_ = s.groupInfoMsgPush(groupId, "'"+_strUserNick+"‘ 发起了群聊", consts.GroupInfo_GroupCreate)
	}(userNick)
	return
}

// 2、GetGroupInfo 获取群基本信息
// 优化返回数据，包含群基本信息和用户在群内的私有信息
func (s *ServerGroup) GetGroupInfo(req *chat.GetGroupInfoReq, userId string) (res *chat.GetGroupInfoRes, err error) {
	// 1. 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 2. 初始化返回结构
	res = &chat.GetGroupInfoRes{}

	// 3. 获取群组基本信息
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 4. 构建群组基本信息
	res.GroupBasicInfo = chat.GroupBasicInfo{
		GroupId:         groupInfo.GroupId,
		GroupName:       groupInfo.GroupName,
		GroupAvatar:     groupInfo.GroupAvatar,
		GroupDesc:       groupInfo.GroupDesc,
		GroupOwnerId:    groupInfo.GroupOwnerId,
		GroupType:       groupInfo.GroupType,
		GroupMaxMembers: groupInfo.GroupMaxMembers,
		GroupCurMembers: groupInfo.GroupCurMembers,
		JoinVerifyType:  groupInfo.JoinVerifyType,
		GroupNotice:     groupInfo.GroupNotice,
		CreatedAt:       tools.GtimeToStringNMDHMS(groupInfo.CreatedAt),
		UpdatedAt:       tools.GtimeToStringNMDHMS(groupInfo.UpdatedAt),
	}

	// 5. 检查用户是否是群成员
	var member modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&member)

	if result.Error == nil {
		if result.RowsAffected > 0 {
			// 是群成员，获取成员基本信息
			userNick, userAvatar := s.getUserInfo(userId)

			// 6. 构建用户在群内的私有信息
			res.GroupPrivateInfo = chat.GroupPrivateInfo{
				UserId:         userId,
				UserNick:       userNick,
				UserAvatar:     userAvatar,
				MemberRole:     member.MemberRole,
				JoinTime:       tools.GtimeToStringNMDHMS(member.JoinTime),
				InviterId:      member.InviterId,
				MemberNick:     member.MemberNick,
				Remarks:        member.Remarks,
				MessageMute:    member.MessageMute,
				GroupTop:       member.GroupTop,
				ShowMemberNick: member.ShowMemberNick,
			}

			// 7. 如果有禁言信息，添加到返回结果中
			now := gtime.Now()
			if member.MuteEndTime != nil && member.MuteEndTime.After(now) {
				res.GroupPrivateInfo.MuteEndTime = tools.GtimeToStringNMDHMS(member.MuteEndTime)
			}
		}
	}

	/*/ 9. 获取群主和管理员信息
	var adminCount int64
	dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND member_role = ? AND deleted_at IS NULL",
		req.GroupId, consts.ChatGroupRole_Admin,
	).Count(&adminCount)

	// 10. 获取群成员数量
	var memberCount int64
	dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).Count(&memberCount)

	// 11. 更新群组基本信息中的成员数量（确保数据一致性）
	res.GroupBasicInfo.GroupCurMembers = int(memberCount)*/

	return
}

// 3、更新群信息
func (s *ServerGroup) UpdateGroupInfo(ctx context.Context, req *chat.UpdateGroupInfoReq, userId string) (res *chat.CreateGroupRes, err error) {
	// 1. 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 2. 检查用户是否有权限更新群信息（群主或管理员）
	var member modelChat.ChatGroupMember
	result := dao.Db.Model(&modelChat.ChatGroupMember{}).
		Select("member_role").
		Where("group_id = ? AND user_id = ? AND member_role IN (?, ?) AND deleted_at IS NULL",
			req.GroupId, userId, consts.ChatGroupRole_Owner, consts.ChatGroupRole_Admin).
		First(&member)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("您没有权限更新群信息")
		}
		return nil, result.Error
	}

	// 3. 获取群组信息
	var groupInfo modelChat.ChatGroupInfo
	result = dao.Db.Model(&modelChat.ChatGroupInfo{}).
		Where("group_id = ? AND deleted_at IS NULL", req.GroupId).
		First(&groupInfo)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 4. 使用请求map判断更新字段
	request := g.RequestFromCtx(ctx)
	requestMap := request.GetMap()

	// 5. 构建更新字段
	updates := make(map[string]interface{})
	var strUpdateInfo string
	curUserNick := tools.GetUserNickFromCtx(ctx)
	strUpdateInfo = "'" + curUserNick + "'"

	//	更新类型
	var strUpdateType string
	// 5.1 群名称
	if _, ok := requestMap["group_name"]; ok && req.GroupName != "" {
		updates["group_name"] = req.GroupName
		strUpdateInfo += " 更新了群名称:'" + req.GroupName + "'"
		strUpdateType = consts.GroupInfo_GroupNameUpdate
	}

	// 5.2 群头像
	if _, ok := requestMap["group_avatar"]; ok && req.GroupAvatar != "" {
		updates["group_avatar"] = req.GroupAvatar

		strUpdateInfo += " 更新了群头像"
		strUpdateType = consts.GroupInfo_GroupAvatarUpdate
	}

	// 5.3 群描述
	if _, ok := requestMap["group_desc"]; ok {
		updates["group_desc"] = req.GroupDesc

		strUpdateInfo += " 更新了群描述"
		strUpdateType = consts.GroupInfo_GroupDescUpdate
	}

	// 5.4 群最大成员数
	if _, ok := requestMap["group_max_members"]; ok && req.GroupMaxMembers > 0 {
		if req.GroupMaxMembers > 200 {
			return nil, errors.New("群最大成员数不能超过200")
		}
		if req.GroupMaxMembers < groupInfo.GroupCurMembers {
			return nil, errors.New("最大成员数不能小于当前成员数")
		}
		updates["group_max_members"] = req.GroupMaxMembers
	}

	// 5.5 入群验证方式
	if _, ok := requestMap["join_verify_type"]; ok && req.JoinVerifyType > 0 {
		if req.JoinVerifyType < 1 || req.JoinVerifyType > 3 {
			return nil, errors.New("入群验证方式无效")
		}
		updates["join_verify_type"] = req.JoinVerifyType
		var strVerifyType string
		if req.JoinVerifyType == consts.GroupJoinType_Freedom {
			strVerifyType = "自由加入"
		} else if req.JoinVerifyType == consts.GroupJoinType_Validate {
			strVerifyType = "需要验证"
		} else if req.JoinVerifyType == consts.GroupJoinType_Forbid {
			strVerifyType = "禁止加入"
		}
		strUpdateInfo += " 更新了群验证方式:'" + strVerifyType + "'"
		strUpdateType = consts.GroupInfo_GroupAddTypeUpdate
	}

	// 5.6 群公告
	if _, ok := requestMap["group_notice"]; ok {
		updates["group_notice"] = req.GroupNotice

		strUpdateInfo += " 更新了群公告" // + req.GroupNotice + "'"
		strUpdateType = consts.GroupInfo_GroupNoticeUpdate
	}

	// 6. 如果没有更新字段，直接返回
	if len(updates) == 0 {
		return nil, errors.New("群信息无更新字段")
	}

	// 7. 更新时间
	now := gtime.Now()
	updates["updated_at"] = now

	// 8. 执行更新
	result = dao.Db.Model(&modelChat.ChatGroupInfo{}).
		Where("group_id = ?", req.GroupId).
		Updates(updates)

	if result.Error != nil {
		return nil, result.Error
	}

	// 9. 获取更新后的群组信息
	res = &chat.CreateGroupRes{
		// 确保任何可能的列表字段都初始化为空数组而不是nil
		GroupBasicInfo: chat.GroupBasicInfo{},
	}

	result = dao.Db.Model(&modelChat.ChatGroupInfo{}).
		Where("group_id = ? AND deleted_at IS NULL", req.GroupId).
		First(&res.GroupBasicInfo)

	if result.Error != nil {
		return nil, result.Error
	}

	go func(_strGroupId, _strUpdateInfo, _strUpdateType string) {
		_ = s.groupInfoMsgPush(_strGroupId, _strUpdateInfo, _strUpdateType)
	}(req.GroupId, strUpdateInfo, strUpdateType)

	return res, nil
}

// 4、解散群组
func (s *ServerGroup) GroupDissolve(ctx context.Context, req *chat.GroupDissolveReq, userId string) (res *chat.GroupDissolveRes, err error) {
	// 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 检查用户是否是群主
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND group_owner_id = ? AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("您不是该群群主或群组不存在")
		}
		return nil, result.Error
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 软删除群组信息
	now := time.Now()
	result = tx.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ?",
		req.GroupId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 软删除所有群成员
	result = tx.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ?",
		req.GroupId,
	).Updates(map[string]interface{}{
		"deleted_at": now,
		"exit_time":  now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	tx.Commit()

	curUserNick := tools.GetUserNickFromCtx(ctx)
	go func(_strUserNick string) {
		_ = s.groupInfoMsgPush(req.GroupId, "'"+_strUserNick+"' 解散了该群", consts.GroupInfo_Disband)
	}(curUserNick)

	// 返回结果
	res = &chat.GroupDissolveRes{
		Message: "群解除成功",
	}

	return
}

// GetGroupList 5、获取群列表
func (s *ServerGroup) GetGroupList(req *chat.GetGroupListReq, userId string) (res *chat.GetGroupListRes, err error) {
	// 1. 初始化响应结构
	res = &chat.GetGroupListRes{
		GroupList: make([]chat.GroupBasicInfo, 0),
	}

	// 2. 查询用户所在的群组ID
	var groupIds []string
	result := dao.Db.Model(&modelChat.ChatGroupMember{}).
		Select("group_id").
		Where("user_id = ? AND deleted_at IS NULL", userId).
		Pluck("group_id", &groupIds)

	if result.Error != nil {
		return nil, result.Error
	}

	// 3. 如果用户没有加入任何群组，直接返回空结果
	if len(groupIds) == 0 {
		return res, nil
	}

	// 4. 查询用户所在群信息，按群名称排序
	var groupInfos []modelChat.ChatGroupInfo
	result = dao.Db.Model(&modelChat.ChatGroupInfo{}).
		Where("group_id IN ? AND deleted_at IS NULL", groupIds).
		Order("group_name ASC"). // 按群名称排序
		Find(&groupInfos)

	if result.Error != nil {
		return nil, result.Error
	}

	// 5. 构建返回数据
	groupList := make([]chat.GroupBasicInfo, len(groupInfos))
	for i, info := range groupInfos {
		groupList[i] = chat.GroupBasicInfo{
			GroupId:         info.GroupId,
			GroupName:       info.GroupName,
			GroupAvatar:     info.GroupAvatar,
			GroupDesc:       info.GroupDesc,
			GroupOwnerId:    info.GroupOwnerId,
			GroupType:       info.GroupType,
			GroupMaxMembers: info.GroupMaxMembers,
			GroupCurMembers: info.GroupCurMembers,
			JoinVerifyType:  info.JoinVerifyType,
			GroupNotice:     info.GroupNotice,
			CreatedAt:       tools.GtimeToStringNMDHMS(info.CreatedAt),
			UpdatedAt:       tools.GtimeToStringNMDHMS(info.UpdatedAt),
		}
	}

	// 6. 设置响应数据
	res.GroupList = groupList
	res.TotalCount = len(groupList)

	return res, nil
}

// GetGroupMembers 6、群-成员获取
// 性能优化点:
// 1. 使用联表查询减少数据库请求次数
// 2. 使用索引加速查询
// 3. 使用预加载减少内存分配
// 4. 使用缓存减少重复计算
func (s *ServerGroup) GetGroupMembers(req *chat.GetGroupMembersReq, userId string) (res *chat.GetGroupMembersRes, err error) {
	// 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100
	}

	// 使用单个查询检查用户是否是群成员
	var isMember int64
	result := dao.Db.Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND user_id = ? AND deleted_at IS NULL", req.GroupId, userId).
		Count(&isMember)

	if result.Error != nil {
		return nil, result.Error
	}

	if isMember == 0 {
		return nil, errors.New("您不是该群成员")
	}

	// 使用单个查询获取成员总数
	var total int64
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND deleted_at IS NULL", req.GroupId).
		Count(&total)

	if result.Error != nil {
		return nil, result.Error
	}

	// 如果没有成员，直接返回空列表
	if total == 0 {
		return &chat.GetGroupMembersRes{
			List:       []chat.GroupPrivateInfo{},
			TotalCount: 0,
		}, nil
	}

	// 使用联表查询同时获取成员信息和用户信息
	// 这样可以减少数据库请求次数，提高性能
	type MemberWithUserInfo struct {
		// 群成员信息
		UserId         string      `gorm:"column:user_id"`
		MemberRole     int         `gorm:"column:member_role"`
		MemberNick     string      `gorm:"column:member_nick"`
		Remarks        string      `gorm:"column:remarks"`
		ShowMemberNick bool        `gorm:"column:show_member_nick"`
		JoinTime       *gtime.Time `gorm:"column:join_time"`
		InviterId      string      `gorm:"column:inviter_id"`
		MuteEndTime    *gtime.Time `gorm:"column:mute_end_time"`
		MessageMute    bool        `gorm:"column:message_mute"`
		GroupTop       bool        `gorm:"column:group_top"`

		// 用户基本信息
		UserNick   string `gorm:"column:user_nick"`
		UserAvatar string `gorm:"column:user_avatar"`
	}

	var membersWithInfo []MemberWithUserInfo

	// 使用左连接查询，确保即使用户信息不存在也能获取到群成员信息
	result = dao.Db.Table("chat_group_members as m").
		Select("m.user_id, m.member_role, m.member_nick, m.remarks, m.show_member_nick, m.join_time, m.inviter_id, m.mute_end_time, m.message_mute, m.group_top, u.user_nick, u.user_avatar").
		Joins("LEFT JOIN user_infos as u ON m.user_id = u.user_id").
		Where("m.group_id = ? AND m.deleted_at IS NULL", req.GroupId).
		Order("m.member_role DESC, m.join_time ASC").
		Offset((req.Page - 1) * req.Size).
		Limit(req.Size).
		Find(&membersWithInfo)

	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	// 预分配切片容量，避免动态扩容
	memberList := make([]chat.GroupPrivateInfo, len(membersWithInfo))

	// 遍历结果集，构建响应数据
	for i, m := range membersWithInfo {
		memberList[i] = chat.GroupPrivateInfo{
			UserId:         m.UserId,
			UserNick:       m.UserNick, // 使用用户昵称作为用户名
			UserAvatar:     m.UserAvatar,
			MemberRole:     m.MemberRole,
			MemberNick:     m.MemberNick,
			Remarks:        m.Remarks,
			ShowMemberNick: m.ShowMemberNick,
			JoinTime:       tools.GtimeToStringNMDHMS(m.JoinTime),
			InviterId:      m.InviterId,
			MuteEndTime:    tools.GtimeToStringNMDHMS(m.MuteEndTime),
			MessageMute:    m.MessageMute,
			GroupTop:       m.GroupTop,
		}
	}

	// 返回结果
	return &chat.GetGroupMembersRes{
		List:       memberList,
		TotalCount: int(total),
	}, nil
}

// 获取用户基本信息（用户昵称和头像）
func (s *ServerGroup) getUserInfo(userId string) (userNick string, userAvatar string) {
	// 这里应该调用用户服务获取用户信息
	// 为简化实现，这里直接从数据库查询
	var userInfo struct {
		UserNick   string
		UserAvatar string
	}

	// 查询用户信息
	dao.Db.Model(&modelUser.UserInfo{}).Select("user_nick, user_avatar").Where("user_id = ?", userId).Scan(&userInfo)

	return userInfo.UserNick, userInfo.UserAvatar
}

// 7、添加群成员
// 1. 批量验证用户ID有效性
// 2. 使用map减少重复查询
// 3. 批量插入减少数据库操作次数
// 4. 根据群验证方式实现不同的添加逻辑:
//   - 自由加入：所有角色拉的好友自动进群
//   - 需要验证：普通用户拉取的好友需要管理员审批，管理员拉的好友自动进群
//   - 禁止加入：普通用户无法拉好友进群，管理员拉的好友自动进群
func (s *ServerGroup) AddGroupMembers(ctx context.Context, req *chat.AddGroupMembersReq, userId string) (res *chat.AddGroupMembersRes, err error) {
	userNick := tools.GetUserNickFromCtx(ctx)
	// 1. 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}
	if len(req.MemberIds) == 0 {
		return nil, errors.New("成员ID列表不能为空")
	}

	// 2. 限制单次添加成员数量，避免过大的事务
	if len(req.MemberIds) > 100 {
		return nil, errors.New("单次最多添加100个成员")
	}

	// 3. 获取群组信息，检查群是否存在
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 4. 检查是否超过最大成员数
	if groupInfo.GroupCurMembers+len(req.MemberIds) > groupInfo.GroupMaxMembers {
		return nil, errors.New("添加成员后将超过群最大成员数限制")
	}

	// 5. 获取当前用户在群中的角色
	var currentMember modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&currentMember)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("您不是该群成员，无法添加成员")
		}
		return nil, result.Error
	}

	// 6. 判断用户权限和群验证方式
	isAdmin := currentMember.MemberRole == consts.ChatGroupRole_Admin || currentMember.MemberRole == consts.ChatGroupRole_Owner

	// 6.1 如果是禁止加入的群，普通用户不能添加成员
	if groupInfo.JoinVerifyType == consts.GroupJoinType_Forbid && !isAdmin {
		return nil, errors.New("该群已设置为禁止加入，只有管理员或群主可以添加成员")
	}

	// 6.2 根据群验证方式和用户角色确定处理方式
	// 自动添加成员的情况：
	// 1. 自由加入的群，所有人都可以直接添加成员
	// 2. 需要验证的群，管理员可以直接添加成员
	// 3. 禁止加入的群，管理员可以直接添加成员
	autoAddMembers := groupInfo.JoinVerifyType == consts.GroupJoinType_Freedom || isAdmin

	// 7. 处理成员添加逻辑
	// 7.1 查询活跃的群成员（未被软删除）
	var activeMembers []struct {
		UserId string
	}
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).
		Select("user_id").
		Where("group_id = ? AND user_id IN ? AND deleted_at IS NULL", req.GroupId, req.MemberIds).
		Find(&activeMembers)

	if result.Error != nil {
		return nil, result.Error
	}

	// 创建活跃成员映射，用于快速查找
	activeMap := make(map[string]bool, len(activeMembers))
	for _, m := range activeMembers {
		activeMap[m.UserId] = true
	}

	// 7.2 查询被软删除的成员
	var deletedMembers []struct {
		UserId string
	}
	result = dao.Db.Unscoped().Model(&modelChat.ChatGroupMember{}).
		Select("user_id").
		Where("group_id = ? AND user_id IN ? AND deleted_at IS NOT NULL", req.GroupId, req.MemberIds).
		Find(&deletedMembers)

	if result.Error != nil {
		return nil, result.Error
	}

	// 创建已删除成员映射
	deletedMap := make(map[string]bool, len(deletedMembers))
	for _, m := range deletedMembers {
		deletedMap[m.UserId] = true
	}

	// 7.3 分类处理成员
	var membersToRestore []string // 需要恢复的成员（已软删除）
	var membersToAdd []string     // 需要新增的成员（从未添加过）
	var membersToRequest []string // 需要申请的成员（普通用户在需要验证的群中添加）

	for _, id := range req.MemberIds {
		if activeMap[id] {
			// 已经是活跃成员，跳过
			continue
		} else if deletedMap[id] {
			// 已被软删除，需要恢复
			if autoAddMembers {
				membersToRestore = append(membersToRestore, id)
			} else {
				membersToRequest = append(membersToRequest, id)
			}
		} else {
			// 从未添加过，需要新增
			if autoAddMembers {
				membersToAdd = append(membersToAdd, id)
			} else {
				membersToRequest = append(membersToRequest, id)
			}
		}
	}

	// 7.4 如果没有需要处理的成员，直接返回
	if len(membersToRestore) == 0 && len(membersToAdd) == 0 && len(membersToRequest) == 0 {
		return &chat.AddGroupMembersRes{
			SuccessCount: 0,
			MemberIds:    []string{},
		}, nil
	}

	// 7.5 验证所有用户ID是否有效
	allMembersToProcess := append(append(membersToRestore, membersToAdd...), membersToRequest...)
	if len(allMembersToProcess) > 0 {
		var validUserCount int64
		result = dao.Db.Model(&modelUser.UserInfo{}).
			Where("user_id IN ? AND deleted_at IS NULL", allMembersToProcess).
			Count(&validUserCount)

		if result.Error != nil {
			return nil, result.Error
		}

		// 如果有无效的用户ID，返回错误
		if int(validUserCount) < len(allMembersToProcess) {
			return nil, errors.New("包含无效的用户ID")
		}
	}

	// 8. 开启事务处理自动添加的成员
	successCount := 0
	successMemberIds := make([]string, 0)
	pendingMemberIds := make([]string, 0)

	if len(membersToRestore) > 0 || len(membersToAdd) > 0 {
		tx := dao.Db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
				err = errors.New("系统错误")
			} else if err != nil {
				tx.Rollback()
			}
		}()

		now := gtime.Now()

		// 8.1 恢复被软删除的成员
		if len(membersToRestore) > 0 {
			// 更新被软删除的成员记录
			result = tx.Unscoped().Model(&modelChat.ChatGroupMember{}).
				Where("group_id = ? AND user_id IN ? AND deleted_at IS NOT NULL", req.GroupId, membersToRestore).
				Updates(map[string]interface{}{
					"deleted_at":  nil,                       // 清除删除时间
					"join_time":   now,                       // 更新加入时间
					"inviter_id":  userId,                    // 更新邀请人
					"updated_at":  now,                       // 更新修改时间
					"member_role": consts.ChatGroupRole_Comm, // 设为普通成员
					"exit_time":   nil,                       // 清除退出时间
				})

			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}

			successCount += int(result.RowsAffected)
			successMemberIds = append(successMemberIds, membersToRestore...)
		}

		// 8.2 添加新成员
		if len(membersToAdd) > 0 {
			// 准备批量插入的数据
			newMembers := make([]modelChat.ChatGroupMember, len(membersToAdd))
			for i, memberId := range membersToAdd {
				newMembers[i] = modelChat.ChatGroupMember{
					GroupId:     req.GroupId,
					UserId:      memberId,
					MemberRole:  consts.ChatGroupRole_Comm, // 设为普通成员
					MemberNick:  "",                        // 默认使用用户昵称
					JoinTime:    now,
					InviterId:   userId, // 邀请人
					MessageMute: false,
					GroupTop:    false,
					CreatedAt:   now,
					UpdatedAt:   now,
				}
			}

			// 批量插入新成员
			result = tx.Create(&newMembers)
			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}

			successCount += len(newMembers)
			successMemberIds = append(successMemberIds, membersToAdd...)
		}

		// 8.3 更新群成员数
		if successCount > 0 {
			result = tx.Model(&modelChat.ChatGroupInfo{}).Where(
				"group_id = ?",
				req.GroupId,
			).Update("group_cur_members", gorm.Expr("group_cur_members + ?", successCount))

			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}
		}

		// 8.4 提交事务
		if err = tx.Commit().Error; err != nil {
			return nil, err
		}
	}

	// 9. 处理需要申请的成员（需要验证的群，普通用户添加）
	if len(membersToRequest) > 0 {
		now := gtime.Now()

		// 9.1 检查是否已有未处理的申请
		var existingRequests []struct {
			UserId string
		}
		result = dao.Db.Model(&modelChat.ChatGroupJoinReq{}).
			Select("user_id").
			Where("group_id = ? AND user_id IN ? AND handle_status = 0", req.GroupId, membersToRequest).
			Find(&existingRequests)

		if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, result.Error
		}

		// 创建已有申请的映射
		existingRequestMap := make(map[string]bool, len(existingRequests))
		for _, r := range existingRequests {
			existingRequestMap[r.UserId] = true
		}

		// 9.2 为没有申请的成员创建申请
		var newRequests []modelChat.ChatGroupJoinReq
		for _, memberId := range membersToRequest {

			if !existingRequestMap[memberId] {
				joinId := uniqueId.GenerateGroupJoinID()

				newRequests = append(newRequests, modelChat.ChatGroupJoinReq{
					GroupId:      req.GroupId,
					UserId:       memberId,
					RequestMsg:   fmt.Sprintf("用户 %s 邀请加入群组", userNick),
					RequestId:    joinId,
					InviterId:    userId,
					HandlerId:    "",
					HandleStatus: 0, // 未处理
					HandleMsg:    "",
					CreatedAt:    now,
					UpdatedAt:    now,
				})
			}
		}

		// 9.3 批量创建申请
		if len(newRequests) > 0 {
			result = dao.Db.Create(&newRequests)
			if result.Error != nil {
				return nil, result.Error
			}
			pendingMemberIds = append(pendingMemberIds, membersToRequest...)
		}
	}

	// 10. 构建返回结果
	res = &chat.AddGroupMembersRes{
		MemberIds:    successMemberIds,
		SuccessCount: successCount,
		PendingCount: len(pendingMemberIds),
		PendingIds:   pendingMemberIds,
	}

	go func(successMemberIds, pendingMemberIds []string, _strUserNick, _strGroupId string) {
		successMemberNicks := server.GetUserNickList(successMemberIds)
		pendingMemberNicks := server.GetUserNickList(pendingMemberIds)
		if len(successMemberNicks) > 0 {
			successMemberNicksStr := strings.Join(successMemberNicks, ",")
			strInfo := "'" + _strUserNick + "' 邀请了 '" + successMemberNicksStr + "' 加入群聊"
			_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_MemberRequestOk)
		}
		if len(pendingMemberNicks) > 0 {
			pendingMemberNicksStr := strings.Join(pendingMemberNicks, ",")

			strInfo := "'" + _strUserNick + "' 申请 '" + pendingMemberNicksStr + "' 加入群聊"
			_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_MemberRequest)
		}
	}(successMemberIds, pendingMemberIds, userNick, req.GroupId)

	return
}

// 8、RemoveGroupMembers 移除群成员
func (s *ServerGroup) RemoveGroupMembers(ctx context.Context, req *chat.RemoveGroupMembersReq, userId string) (res *chat.RemoveGroupMembersRes, err error) {
	// 1. 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}
	if len(req.MemberIds) == 0 {
		return nil, errors.New("成员ID列表不能为空")
	}

	// 1.1 限制单次移除成员数量，避免过大的事务
	if len(req.MemberIds) > 100 {
		return nil, errors.New("单次最多移除100个成员")
	}

	// 2. 获取群组信息
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 3. 检查用户是否有权限移除成员（群主或管理员）
	var currentMember modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND member_role IN (1, 2) AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&currentMember)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("您没有权限移除群成员")
		}
		return nil, result.Error
	}

	// 4. 性能优化: 批量查询要移除的成员信息
	var membersToRemove []modelChat.ChatGroupMember

	result = dao.Db.Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND user_id IN ? AND deleted_at IS NULL", req.GroupId, req.MemberIds).
		Find(&membersToRemove)

	// 处理查询错误
	if result.Error != nil {
		fmt.Printf("查询错误: %v\n", result.Error)
		return nil, result.Error
	}

	// 4.5 如果没有找到任何成员，直接返回
	if len(membersToRemove) == 0 {
		return &chat.RemoveGroupMembersRes{
			MemberIds:    []string{},
			SuccessCount: 0,
		}, nil
	}

	// 5. 创建成员ID到角色的映射，用于权限检查
	memberRoleMap := make(map[string]int, len(membersToRemove))
	validMemberIds := make([]string, 0, len(membersToRemove))

	for _, member := range membersToRemove {
		memberRoleMap[member.UserId] = member.MemberRole
		validMemberIds = append(validMemberIds, member.UserId)
	}

	// 6. 过滤掉不能移除的成员
	var membersToActuallyRemove []string
	for _, memberId := range validMemberIds {
		// 6.1 不能移除群主
		if memberId == groupInfo.GroupOwnerId {
			continue
		}
		// 6.2 管理员不能移除其他管理员（只有群主可以）
		if currentMember.MemberRole == 1 && memberRoleMap[memberId] == 1 {
			continue
		}
		membersToActuallyRemove = append(membersToActuallyRemove, memberId)
	}

	// 6.3 如果没有可以移除的成员，直接返回
	if len(membersToActuallyRemove) == 0 {
		return &chat.RemoveGroupMembersRes{
			SuccessCount: 0,
			MemberIds:    []string{},
		}, nil
	}

	// 7. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 8. 批量软删除成员记录
	// 8.1 检查是否有成员需要移除
	if len(membersToActuallyRemove) == 0 {
		tx.Rollback()
		return &chat.RemoveGroupMembersRes{
			MemberIds:    []string{},
			SuccessCount: 0,
		}, nil
	}

	// 8.2 使用安全的批量更新方式
	now := time.Now()

	// 构建更新条件
	updateQuery := tx.Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND deleted_at IS NULL", req.GroupId)

	// 使用IN查询，确保membersToActuallyRemove不为空
	updateQuery = updateQuery.Where("user_id IN ?", membersToActuallyRemove)

	// 执行批量更新
	result = updateQuery.Updates(map[string]interface{}{
		"deleted_at": now,
		"exit_time":  now,
	})

	if result.Error != nil {
		fmt.Printf("更新错误: %v\n", result.Error)
		tx.Rollback()
		return nil, result.Error
	}

	// 9. 获取实际移除的成员数量
	successCount := int(result.RowsAffected)

	// 10. 如果成功移除了成员，更新群成员数
	if successCount > 0 {
		result = tx.Model(&modelChat.ChatGroupInfo{}).Where(
			"group_id = ?",
			req.GroupId,
		).Update("group_cur_members", gorm.Expr("group_cur_members - ?", successCount))

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 11. 提交事务
	tx.Commit()

	// 12. 返回结果 ，构建成功移除的成员ID列表
	var successMemberIds []string
	if successCount > 0 {
		if successCount == len(membersToActuallyRemove) {
			// 如果全部成功移除，直接使用原列表
			successMemberIds = make([]string, len(membersToActuallyRemove))
			copy(successMemberIds, membersToActuallyRemove)
		} else {
			// 重新查询确认哪些成员被成功移除， 如果实际移除的成员数量与预期不符，可能是因为有些成员已经被删除
			var removedMembers []struct {
				UserId string
			}

			// 查询哪些成员被成功移除（通过检查删除时间）
			result = dao.Db.Model(&modelChat.ChatGroupMember{}).
				Select("user_id").
				Where("group_id = ? AND user_id IN ? AND deleted_at = ?",
					req.GroupId, membersToActuallyRemove, now).
				Find(&removedMembers)

			// 打印查询结果
			fmt.Printf("查询到的已移除成员: %d\n", len(removedMembers))

			if result.Error == nil && len(removedMembers) > 0 {
				// 构建成功移除的成员ID列表
				successMemberIds = make([]string, len(removedMembers))
				for i, member := range removedMembers {
					successMemberIds[i] = member.UserId
				}
			} else {
				// 如果查询失败，使用原列表（可能不准确）
				successMemberIds = make([]string, len(membersToActuallyRemove))
				copy(successMemberIds, membersToActuallyRemove)
			}
		}
	} else {
		// 如果没有成功移除任何成员，返回空列表
		successMemberIds = []string{}
	}

	res = &chat.RemoveGroupMembersRes{
		MemberIds:    successMemberIds,
		SuccessCount: successCount,
	}

	userNick := tools.GetUserNickFromCtx(ctx)
	go func(successMemberIds []string, _strUserNick, _strGroupId string) {
		successMemberNicks := server.GetUserNickList(successMemberIds)
		if len(successMemberNicks) > 0 {
			successMemberNicksStr := strings.Join(successMemberNicks, ",")
			strInfo := "'" + _strUserNick + "' 移除了 '" + successMemberNicksStr + "'"
			//	发送给群管理员
			_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_MemberRemove)
			//	发送个被移除者
			_ = s.groupInfoMsgPush(_strGroupId, successMemberIds, consts.GroupInfo_MemberRemoved)
		}
	}(successMemberIds, userNick, req.GroupId)

	return
}

// 9、设置群管理员
func (s *ServerGroup) SetGroupAdmin(req *chat.SetGroupAdminReq, userId string) (res *chat.SetGroupAdminRes, err error) {
	// 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}
	if req.UserId == "" {
		return nil, errors.New("用户ID不能为空")
	}

	// 获取群组信息
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 检查操作用户是否是群主
	if groupInfo.GroupOwnerId != userId {
		return nil, errors.New("只有群主才能设置管理员")
	}

	// 检查目标用户是否是群成员
	var targetMember modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.GroupId, req.UserId,
	).First(&targetMember)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("该用户不是群成员")
		}
		return nil, result.Error
	}

	// 不能对群主进行操作
	if targetMember.MemberRole == consts.ChatGroupRole_Owner {
		return nil, errors.New("不能对群主进行此操作")
	}

	// 设置或取消管理员角色
	tips := "已成功设置为普通用户"
	newRole := consts.ChatGroupRole_Comm // 默认为普通成员
	if req.IsAdmin {
		newRole = consts.ChatGroupRole_Admin // 设为管理员
		tips = "已成功设置为群管理员"
	}

	// 如果角色没有变化，直接返回成功
	if targetMember.MemberRole == newRole {
		return &chat.SetGroupAdminRes{Message: "角色未发生变化"}, nil
	}

	// 更新成员角色
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ?",
		req.GroupId, req.UserId,
	).Update("member_role", newRole)

	if result.Error != nil {
		return nil, result.Error
	}

	// 返回结果
	res = &chat.SetGroupAdminRes{
		Message: tips,
	}
	//	发送通知消息
	go func(_strGroupId, strTargetUserId, strTargetUserNick, _strInfo string) {
		if strTargetUserNick != "" {
			//	设置过群昵称
			_strInfo = "'" + strTargetUserNick + "'" + _strInfo
		} else {
			//	没有设置群昵称
			userNick, _ := server.GetUserInfo(strTargetUserId)
			_strInfo = "'" + userNick + _strInfo + "'"
		}
		_ = s.groupInfoMsgPush(_strGroupId, _strInfo, consts.GroupInfo_AdminUpdate)
	}(req.GroupId, targetMember.UserId, targetMember.MemberNick, tips)
	return
}

// TransferGroupOwner 10、转让群主
// 此接口用于将群主身份转让给群内其他成员
// 转让后原群主变为管理员，新群主获得所有群主权限
func (s *ServerGroup) TransferGroupOwner(req *chat.TransferGroupOwnerReq, userId string) (res *chat.TransferGroupOwnerRes, err error) {
	// 1. 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}
	if req.NewOwnerId == "" {
		return nil, errors.New("新群主ID不能为空")
	}

	// 1.1 不能转让给自己
	if userId == req.NewOwnerId {
		return nil, errors.New("不能转让给自己")
	}

	// 2. 使用单次查询获取群组信息和当前用户的成员信息
	var (
		groupInfo      modelChat.ChatGroupInfo
		newOwnerMember modelChat.ChatGroupMember
	)

	// 2.1 获取群组信息
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).
		Select("group_id, group_owner_id, group_name").
		Where("group_id = ? AND deleted_at IS NULL", req.GroupId).
		First(&groupInfo)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, fmt.Errorf("查询群组信息失败: %w", result.Error)
	}

	// 2.2 检查操作用户是否是群主
	if groupInfo.GroupOwnerId != userId {
		return nil, errors.New("只有群主才能转让群主身份")
	}

	// 2.3 检查新群主是否是群成员
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).
		Select("user_id, member_role").
		Where("group_id = ? AND user_id = ? AND deleted_at IS NULL", req.GroupId, req.NewOwnerId).
		First(&newOwnerMember)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("新群主不是群成员")
		}
		return nil, fmt.Errorf("查询新群主信息失败: %w", result.Error)
	}

	// 3. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		} else if err != nil {
			tx.Rollback()
		}
	}()

	now := gtime.Now()

	// 3.1 使用批量更新减少数据库操作次数
	// 准备更新数据
	updates := []struct {
		model     interface{}
		condition map[string]interface{}
		values    map[string]interface{}
	}{
		{
			// 更新群组信息中的群主ID
			model:     &modelChat.ChatGroupInfo{},
			condition: map[string]interface{}{"group_id": req.GroupId},
			values:    map[string]interface{}{"group_owner_id": req.NewOwnerId, "updated_at": now},
		},
		{
			// 更新原群主为管理员
			model:     &modelChat.ChatGroupMember{},
			condition: map[string]interface{}{"group_id": req.GroupId, "user_id": userId},
			values:    map[string]interface{}{"member_role": consts.ChatGroupRole_Admin, "updated_at": now},
		},
		{
			// 更新新群主的角色
			model:     &modelChat.ChatGroupMember{},
			condition: map[string]interface{}{"group_id": req.GroupId, "user_id": req.NewOwnerId},
			values:    map[string]interface{}{"member_role": consts.ChatGroupRole_Owner, "updated_at": now},
		},
	}

	// 3.2 执行批量更新
	for _, update := range updates {
		result = tx.Model(update.model).Where(update.condition).Updates(update.values)
		if result.Error != nil {
			// 错误已经在defer中处理，这里只需设置err
			err = fmt.Errorf("群转让失败: %w", result.Error)
			return nil, err
		}

		// 验证更新是否成功
		if result.RowsAffected == 0 {
			err = fmt.Errorf("群转让失败: 未找到记录")
			return nil, err
		}
	}

	// 3.3 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 4. 获取新旧群主的用户信息，用于返回更详细的结果
	oldOwnerNick, _ := s.getUserInfo(userId)
	newOwnerNick, _ := s.getUserInfo(req.NewOwnerId)

	// 5. 返回结果
	res = &chat.TransferGroupOwnerRes{
		GroupId:      req.GroupId,
		GroupName:    groupInfo.GroupName,
		OldOwnerId:   userId,
		OldOwnerNick: oldOwnerNick,
		NewOwnerId:   req.NewOwnerId,
		NewOwnerNick: newOwnerNick,
		TransferTime: now.Format("2006-01-02 15:04:05"),
	}

	go func(_strNewOwnerNick, _strGroupId string) {
		strInfo := " 此群主已转让为:'" + _strNewOwnerNick + "'"
		_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_OwnerTransfer)

	}(newOwnerNick, req.GroupId)
	return
}

// 11、退出群组
func (s *ServerGroup) QuitGroup(ctx context.Context, req *chat.QuitGroupReq, userId string) (res *chat.QuitGroupRes, err error) {
	// 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 获取群组信息
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 检查用户是否是群成员
	var member modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&member)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("您不是该群成员")
		}
		return nil, result.Error
	}

	// 群主不能直接退出群组，需要先转让群主
	if groupInfo.GroupOwnerId == userId {
		return nil, errors.New("群主不能直接退出群组，请先转让群主身份")
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 软删除成员记录
	now := time.Now()
	result = tx.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ?",
		req.GroupId, userId,
	).Updates(map[string]interface{}{
		"deleted_at": now,
		"exit_time":  now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 更新群成员数
	result = tx.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ?",
		req.GroupId,
	).Update("group_cur_members", groupInfo.GroupCurMembers-1)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	tx.Commit()

	// 返回结果
	res = &chat.QuitGroupRes{
		Success: true,
	}
	curUserNick := tools.GetUserNickFromCtx(ctx)
	go func(_strUserNick, _strGroupId string) {
		strInfo := "'" + _strUserNick + "' 已退出群聊"
		_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_MemberQuit)
	}(curUserNick, req.GroupId)

	return
}

// 12、GroupPrivateInfoUpdate 更新群成员私有信息 ,用于更新用户在群组中的个人设置，如群昵称、消息免打扰、置顶等
func (s *ServerGroup) GroupPrivateInfoUpdate(ctx context.Context, req *chat.GroupPrivateInfoUpdateReq, userId string) (res *chat.GroupPrivateInfoUpdateRes, err error) {
	// 1. 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 2. 检查群组是否存在
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 3. 检查用户是否是群成员
	var member modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&member)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("您不是该群成员")
		}
		return nil, result.Error
	}

	// 4. 构建更新字段
	updates := make(map[string]interface{})

	// 获取原始请求对象， 判断用户是否传输了这个数据
	request := g.RequestFromCtx(ctx)
	requestMap := request.GetMap()
	var bIsUpdateMemberNick bool = false
	// 4.1 更新群昵称
	if _, ok := requestMap["member_nick"]; ok {
		updates["member_nick"] = req.MemberNick
		bIsUpdateMemberNick = true
	}
	// 4.2 更新消息免打扰设置
	if _, ok := requestMap["message_mute"]; ok {
		updates["message_mute"] = req.MessageMute
	}
	// 4.3 更新置顶设置
	if _, ok := requestMap["group_top"]; ok {
		updates["group_top"] = req.GroupTop
	}
	// 4.4 更新是否显示群成员昵称
	if _, ok := requestMap["show_member_nick"]; ok {
		updates["show_member_nick"] = req.ShowMemberNick
	}
	// 4.5群备注
	if _, ok := requestMap["remarks"]; ok {
		updates["remarks"] = req.Remarks
	}

	if len(updates) <= 0 {
		return nil, errors.New("未传输修改内容")
	}
	// 4.5 更新时间
	now := gtime.Now()
	updates["updated_at"] = now

	// 5. 执行更新
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ?",
		req.GroupId, userId,
	).Updates(updates)

	if result.Error != nil {
		return nil, result.Error
	}

	// 6. 获取用户基本信息
	userNick, userAvatar := s.getUserInfo(userId)

	// 7. 获取更新后的成员信息
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&member)

	if result.Error != nil {
		return nil, result.Error
	}

	// 8. 构建返回结果
	res = &chat.GroupPrivateInfoUpdateRes{
		GroupPrivateInfo: chat.GroupPrivateInfo{
			UserId:         userId,
			UserNick:       userNick,
			UserAvatar:     userAvatar,
			MemberRole:     member.MemberRole,
			JoinTime:       tools.GtimeToStringNMDHMS(member.JoinTime),
			InviterId:      member.InviterId,
			MemberNick:     member.MemberNick,
			Remarks:        member.Remarks,
			MessageMute:    member.MessageMute,
			GroupTop:       member.GroupTop,
			ShowMemberNick: member.ShowMemberNick,
		},
	}

	// 9. 如果有禁言信息，添加到返回结果中
	if member.MuteEndTime != nil && member.MuteEndTime.After(now) {
		res.GroupPrivateInfo.MuteEndTime = tools.GtimeToStringNMDHMS(member.MuteEndTime)
	}
	//	修改了群昵称，发送消息
	if bIsUpdateMemberNick {
		//	发送通知消息
		go func(_strGroupId, strUserNick string) {
			_ = s.groupInfoMsgPush(_strGroupId, "'"+strUserNick+"' 修改了在群显示昵称", consts.GroupInfo_MemberNickUpdate)
		}(req.GroupId, userNick)
	}

	return
}

// 12、MuteGroupMember 设置群成员禁言
func (s *ServerGroup) MuteGroupMember(req *chat.MuteGroupMemberReq, userId string, userNick string) (res *chat.MuteGroupMemberRes, err error) {
	// 1. 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}
	if req.UserId == "" {
		return nil, errors.New("用户ID不能为空")
	}

	// 2. 获取群组信息
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 3. 检查操作用户是否有权限（群主或管理员）
	var operatorMember modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND member_role IN (1, 2) AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&operatorMember)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("您没有权限设置禁言")
		}
		return nil, result.Error
	}

	// 4. 检查目标用户是否是群成员
	var targetMember modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.GroupId, req.UserId,
	).First(&targetMember)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("该用户不是群成员")
		}
		return nil, result.Error
	}

	// 5. 权限检查
	// 5.1 不能对群主进行禁言
	if targetMember.MemberRole == consts.ChatGroupRole_Owner {
		return nil, errors.New("不能对群主进行禁言")
	}

	// 5.2 管理员不能对其他管理员进行禁言，只有群主可以
	if operatorMember.MemberRole == consts.ChatGroupRole_Admin && targetMember.MemberRole == consts.ChatGroupRole_Admin {
		return nil, errors.New("管理员不能对其他管理员进行禁言")
	}

	// 6. 获取目标用户的基本信息
	targetUserName, _ := s.getUserInfo(req.UserId)

	// 7. 计算禁言结束时间
	var muteEndTime *gtime.Time
	var tips string
	var canSpeakTimeDesc string
	var muteEndTimeStr string

	now := gtime.Now()

	if req.MuteUntil != nil {
		// 7.1 使用指定的禁言截止时间
		muteEndTime = req.MuteUntil
		if muteEndTime.Before(now) {
			// 如果指定的时间已经过去，则解除禁言
			muteEndTime = nil
			tips = fmt.Sprintf("已解除对用户 '%s' 的禁言", targetUserName)
			canSpeakTimeDesc = "现在"
		} else {
			tips = fmt.Sprintf("已将用户 '%s' 禁言至 %s", targetUserName, muteEndTime.Format("2006-01-02 15:04:05"))
			canSpeakTimeDesc = s.getHumanReadableTimeDescription(muteEndTime)
			muteEndTimeStr = muteEndTime.Format("2006-01-02 15:04:05")
		}
	} else if req.MuteTime > 0 {
		// 7.2 根据禁言时长计算截止时间
		//endTime := now.Add(time.Duration(req.MuteTime) * time.Minute)
		muteEndTime = now.Add(time.Duration(req.MuteTime) * time.Minute)
		tips = fmt.Sprintf("已将用户 '%s' 禁言 %d 分钟", targetUserName, req.MuteTime)
		canSpeakTimeDesc = s.getHumanReadableTimeDescription(muteEndTime)
		muteEndTimeStr = tools.GtimeToStringNMDHMS(muteEndTime)
	} else {
		// 7.3 解除禁言
		muteEndTime = nil
		tips = fmt.Sprintf("已解除对用户 '%s' 的禁言", targetUserName)
		canSpeakTimeDesc = "现在"
	}

	// 8. 更新禁言状态
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ?",
		req.GroupId, req.UserId,
	).Update("mute_end_time", muteEndTime)

	if result.Error != nil {
		return nil, result.Error
	}

	// 9. 返回结果
	res = &chat.MuteGroupMemberRes{
		Message:      tips,
		MuteEndTime:  muteEndTimeStr,
		CanSpeakTime: canSpeakTimeDesc,
	}

	go func(_strUserNick, _strGroupId, _strInfo string) {
		_strInfo = "'" + _strUserNick + "'" + _strInfo
		_ = s.groupInfoMsgPush(_strGroupId, _strInfo, consts.GroupInfo_MemberMute)
	}(userNick, req.GroupId, tips)

	return
}

// getHumanReadableTimeDescription 获取人性化的时间描述
// 例如：10分钟后、1小时后、明天10:30、后天15:00等
func (s *ServerGroup) getHumanReadableTimeDescription(t *gtime.Time) string {
	now := gtime.Now()
	duration := t.Sub(now)

	// 1. 如果时间已经过去，返回"现在"
	if duration <= 0 {
		return "现在"
	}

	// 2. 如果时间在1小时内
	if duration < time.Hour {
		minutes := int(duration.Minutes())
		if minutes <= 1 {
			return "1分钟后"
		}
		return fmt.Sprintf("%d分钟后", minutes)
	}

	// 3. 如果时间在24小时内
	if duration < 24*time.Hour {
		hours := int(duration.Hours())
		if hours == 1 {
			return "1小时后"
		}
		return fmt.Sprintf("%d小时后", hours)
	}

	// 4. 如果是明天
	tomorrow := now.AddDate(0, 0, 1)
	if t.Year() == tomorrow.Year() && t.Month() == tomorrow.Month() && t.Day() == tomorrow.Day() {
		return fmt.Sprintf("明天%s", t.Format("15:04"))
	}

	// 5. 如果是后天
	dayAfterTomorrow := now.AddDate(0, 0, 2)
	if t.Year() == dayAfterTomorrow.Year() && t.Month() == dayAfterTomorrow.Month() && t.Day() == dayAfterTomorrow.Day() {
		return fmt.Sprintf("后天%s", t.Format("15:04"))
	}

	// 6. 如果是本周内
	daysUntil := int(duration.Hours() / 24)
	if daysUntil < 7 {
		weekdays := []string{"周日", "周一", "周二", "周三", "周四", "周五", "周六"}
		return fmt.Sprintf("%s%s", weekdays[t.Weekday()], t.Format("15:04"))
	}

	// 7. 如果是一周以上，返回具体日期和时间
	return t.Format("2006-01-02 15:04")
}

// 13、申请加入群组
func (s *ServerGroup) JoinGroup(ctx context.Context, req *chat.JoinGroupReq, userId string) (res *chat.JoinGroupRes, err error) {
	// 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 1.获取群组信息
	var groupInfo modelChat.ChatGroupInfo
	result := dao.Db.Model(&modelChat.ChatGroupInfo{}).Where(
		"group_id = ? AND deleted_at IS NULL",
		req.GroupId,
	).First(&groupInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("群组不存在或已解散")
		}
		return nil, result.Error
	}

	// 2. 检查用户是否有群成员记录（未删除或被软删除）
	bIsDeleted := false
	var existMember modelChat.ChatGroupMember
	result = dao.Db.Unscoped().Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ?",
		req.GroupId, userId,
	).First(&existMember)

	if result.Error == nil && result.RowsAffected > 0 {
		if existMember.DeletedAt == nil {
			// 用户已经是群成员
			return &chat.JoinGroupRes{
				Success: true,
				Message: "您已经是该群成员, 无需申请",
			}, nil
		} else {
			//	用户已软删除过
			bIsDeleted = true
		}

	} else if result.Error != gorm.ErrRecordNotFound {
		// 其他错误
		return nil, result.Error
	}

	// 3.检查群组是否已满
	if groupInfo.GroupCurMembers >= groupInfo.GroupMaxMembers {
		return nil, errors.New("群组已达到最大成员数限制")
	}

	// 4. 检查入群验证方式
	switch groupInfo.JoinVerifyType {
	case consts.GroupJoinType_Freedom: // 自由加入
		// 开启事务
		tx := dao.Db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
				err = errors.New("系统错误")
			}
		}()

		now := gtime.Now()

		if bIsDeleted {
			// 4.1 如果用户曾经是群成员（被软删除），恢复记录
			// 性能优化: 只更新必要的字段
			updates := map[string]interface{}{
				"deleted_at":   nil,                       // 清除删除时间
				"join_time":    now,                       // 更新加入时间
				"exit_time":    nil,                       // 清除退出时间
				"updated_at":   now,                       // 更新修改时间
				"member_role":  consts.ChatGroupRole_Comm, // 设为普通成员
				"message_mute": false,                     // 重置消息免打扰设置
				"group_top":    false,                     // 重置置顶设置
			}

			// 如果原记录有邀请人，保留原邀请人
			if existMember.InviterId == "" {
				updates["inviter_id"] = "" // 自由加入没有邀请人
			}

			// 更新记录
			result = tx.Unscoped().Model(&modelChat.ChatGroupMember{}).Where(
				"group_id = ? AND user_id = ?",
				req.GroupId, userId,
			).Updates(updates)

			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}
		} else {
			// 4.2 如果用户从未是群成员，创建新记录
			newMember := &modelChat.ChatGroupMember{
				GroupId:     req.GroupId,
				UserId:      userId,
				MemberRole:  consts.ChatGroupRole_Comm, // 普通成员
				MemberNick:  "",                        // 默认使用用户昵称
				JoinTime:    now,
				InviterId:   "", // 自由加入没有邀请人
				MessageMute: false,
				GroupTop:    false,
				CreatedAt:   now,
				UpdatedAt:   now,
			}

			// 添加成员
			result = tx.Create(newMember)
			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}
		}

		// 4. 更新群成员数
		result = tx.Model(&modelChat.ChatGroupInfo{}).Where(
			"group_id = ?",
			req.GroupId,
		).Update("group_cur_members", groupInfo.GroupCurMembers+1)

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}

		// 5. 提交事务
		tx.Commit()

		msg := "已成功加入群组"
		if bIsDeleted {
			msg = "已成功重新加入群组"
		}
		curUserNick := tools.GetUserNickFromCtx(ctx)
		go func(_strUserNick, _strGroupId string) {
			strInfo := "'" + _strUserNick + "' 已加入群聊"
			_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_MemberRequest)
		}(curUserNick, req.GroupId)

		// 6. 返回结果
		return &chat.JoinGroupRes{
			Success: true,
			Message: msg,
		}, nil

	case consts.GroupJoinType_Validate: // 需要验证
		// 检查是否已有未处理的申请
		var existRequest modelChat.ChatGroupJoinReq
		result = dao.Db.Model(&modelChat.ChatGroupJoinReq{}).Where(
			"group_id = ? AND user_id = ? AND handle_status = 0",
			req.GroupId, userId,
		).First(&existRequest)

		if result.Error == nil {
			// 已有未处理的申请
			return &chat.JoinGroupRes{
				Success: true,
				Message: "您已提交过入群申请，请等待管理员审核",
			}, nil
		} else if result.Error != gorm.ErrRecordNotFound {
			// 其他错误
			return nil, result.Error
		}

		joinId := uniqueId.GenerateGroupJoinID()
		// 创建入群申请
		now := time.Now()
		joinRequest := &modelChat.ChatGroupJoinReq{
			GroupId:      req.GroupId,
			UserId:       userId,
			RequestMsg:   req.RequestMsg,
			RequestId:    joinId,
			InviterId:    "",
			HandlerId:    "",
			HandleStatus: 0, // 未处理
			HandleMsg:    "",
			Model: gorm.Model{
				CreatedAt: now,
				UpdatedAt: now,
			},
		}

		result = dao.Db.Create(joinRequest)
		if result.Error != nil {
			return nil, result.Error
		}
		curUserNick := tools.GetUserNickFromCtx(ctx)
		go func(_strUserNick, _strGroupId string) {
			strInfo := "'" + _strUserNick + "' 申请加入群聊"
			_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_MemberRequest)
		}(curUserNick, req.GroupId)

		return &chat.JoinGroupRes{
			Success: true,
			Message: "入群申请已提交，请等待管理员审核",
		}, nil

	case consts.GroupJoinType_Forbid: // 禁止加入
		return nil, errors.New("该群组已设置为禁止加入")

	default:
		return nil, errors.New("未知的入群验证方式")
	}
}

// 14、获取入群申请列表
func (s *ServerGroup) GetJoinRequests(req *chat.GetJoinRequestsReq, userId string) (res *chat.GetJoinRequestsRes, err error) {
	// 参数校验
	if req.GroupId == "" {
		return nil, errors.New("群组ID不能为空")
	}

	// 检查用户是否有权限查看入群申请（群主或管理员）
	var member modelChat.ChatGroupMember
	result := dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND member_role IN (1, 2) AND deleted_at IS NULL",
		req.GroupId, userId,
	).First(&member)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("您没有权限查看入群申请")
		}
		return nil, result.Error
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100
	}

	// 构建查询条件
	query := dao.Db.Model(&modelChat.ChatGroupJoinReq{}).Where(
		"group_id = ?",
		req.GroupId,
	)

	// 根据状态筛选
	if req.Status > 0 {
		query = query.Where("handle_status = ?", req.Status)
	}

	// 获取申请总数
	var total int64
	result = query.Count(&total)
	if result.Error != nil {
		return nil, result.Error
	}

	// 获取申请列表
	var joinRequests []modelChat.ChatGroupJoinReq
	result = query.Order("created_at DESC").
		Offset((req.Page - 1) * req.Size).
		Limit(req.Size).
		Find(&joinRequests)

	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	var requestList []chat.JoinRequestInfo
	for _, req := range joinRequests {
		// 获取申请用户的基本信息
		userName, userAvatar := s.getUserInfo(req.UserId)

		// 构建申请信息
		requestInfo := chat.JoinRequestInfo{
			RequestId:    req.RequestId,
			GroupId:      req.GroupId,
			UserId:       req.UserId,
			UserName:     userName,
			UserAvatar:   userAvatar,
			RequestMsg:   req.RequestMsg,
			InviterId:    req.InviterId,
			HandlerId:    req.HandlerId,
			HandleStatus: req.HandleStatus,
			HandleMsg:    req.HandleMsg,
			HandleTime:   tools.GtimeToStringNMDHMS(req.UpdatedAt),
			CreatedAt:    tools.GtimeToStringNMDHMS(req.CreatedAt),
		}

		requestList = append(requestList, requestInfo)
	}

	// 返回结果
	res = &chat.GetJoinRequestsRes{
		List:       requestList,
		TotalCount: int(total),
	}

	return
}

// 15、处理入群申请
func (s *ServerGroup) HandleJoinRequest(ctx context.Context, req *chat.HandleJoinRequestReq, userId string) (res *chat.HandleJoinRequestRes, err error) {
	// 参数校验
	if req.RequestId == "" {
		return nil, errors.New("申请记录ID不能为空")
	}

	// 获取申请记录
	var joinRequest modelChat.ChatGroupJoinReq
	result := dao.Db.Model(&modelChat.ChatGroupJoinReq{}).Where(
		"request_id = ?",
		req.RequestId,
	).First(&joinRequest)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("申请记录不存在")
		}
		return nil, result.Error
	}

	// 检查申请是否已处理
	if joinRequest.HandleStatus != 0 {
		return nil, errors.New("该申请已处理")
	}

	// 检查用户是否有权限处理申请（群主或管理员）
	var member modelChat.ChatGroupMember
	result = dao.Db.Model(&modelChat.ChatGroupMember{}).Where(
		"group_id = ? AND user_id = ? AND member_role IN (1, 2) AND deleted_at IS NULL",
		joinRequest.GroupId, userId,
	).First(&member)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("您没有权限处理入群申请")
		}
		return nil, result.Error
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 更新申请状态
	handleStatus := 2 // 默认拒绝
	if req.Approved {
		handleStatus = 1 // 同意
	}

	now := time.Now()
	result = tx.Model(&modelChat.ChatGroupJoinReq{}).Where(
		"request_id = ?",
		req.RequestId,
	).Updates(map[string]interface{}{
		"handle_status": handleStatus,
		"handle_msg":    req.HandleMsg,
		"handler_id":    userId,
		"updated_at":    now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果同意申请，添加用户为群成员
	if req.Approved {
		// 2. 获取群组信息
		var groupInfo modelChat.ChatGroupInfo
		result = tx.Model(&modelChat.ChatGroupInfo{}).Where(
			"group_id = ? AND deleted_at IS NULL",
			joinRequest.GroupId,
		).First(&groupInfo)

		if result.Error != nil {
			tx.Rollback()
			if result.Error == gorm.ErrRecordNotFound {
				return nil, errors.New("群组不存在或已解散")
			}
			return nil, result.Error
		}

		// 2. 检查是否超过最大成员数
		if groupInfo.GroupCurMembers >= groupInfo.GroupMaxMembers {
			tx.Rollback()
			return nil, errors.New("群成员数已达上限")
		}

		// 3. 一次性查询用户的成员记录（包括已删除的）
		var memberRecord modelChat.ChatGroupMember
		result = tx.Unscoped().Model(&modelChat.ChatGroupMember{}).Where(
			"group_id = ? AND user_id = ?",
			joinRequest.GroupId, joinRequest.UserId,
		).First(&memberRecord)

		// 2. 根据查询结果判断用户状态
		nowTime := gtime.Now()
		if result.Error == nil {
			// 找到记录，判断是否被软删除
			if result.RowsAffected > 0 {
				if memberRecord.DeletedAt != nil {
					//	软删除过
					// 4.1 如果用户曾经是群成员（被软删除），恢复记录
					// 性能优化: 只更新必要的字段
					updates := map[string]interface{}{
						"deleted_at":   nil,                       // 清除删除时间
						"join_time":    nowTime,                   // 更新加入时间
						"exit_time":    nil,                       // 清除退出时间
						"updated_at":   nowTime,                   // 更新修改时间
						"member_role":  consts.ChatGroupRole_Comm, // 设为普通成员
						"message_mute": false,                     // 重置消息免打扰设置
						"group_top":    false,                     // 重置置顶设置
					}

					// 保留原邀请人，如果没有则使用当前申请中的邀请人
					if memberRecord.InviterId == "" && joinRequest.InviterId != "" {
						updates["inviter_id"] = joinRequest.InviterId
					}

					// 更新记录
					result = tx.Unscoped().Model(&modelChat.ChatGroupMember{}).Where(
						"group_id = ? AND user_id = ?",
						joinRequest.GroupId, joinRequest.UserId,
					).Updates(updates)

					if result.Error != nil {
						tx.Rollback()
						return nil, result.Error
					}
				}

			} else {
				// 4.2 如果用户从未是群成员，创建新记录
				newMember := &modelChat.ChatGroupMember{
					GroupId:     joinRequest.GroupId,
					UserId:      joinRequest.UserId,
					MemberRole:  consts.ChatGroupRole_Comm, // 普通成员
					MemberNick:  "",                        // 默认使用用户昵称
					JoinTime:    nowTime,
					InviterId:   joinRequest.InviterId,
					MessageMute: false,
					GroupTop:    false,
					CreatedAt:   nowTime,
					UpdatedAt:   nowTime,
				}

				result = tx.Create(newMember)
				if result.Error != nil {
					tx.Rollback()
					return nil, result.Error
				}

				// 记录日志
				fmt.Printf("创建用户 %s 在群组 %s 的新成员记录\n",
					joinRequest.UserId, joinRequest.GroupId)
			}

			// 5. 更新群成员数
			result = tx.Model(&modelChat.ChatGroupInfo{}).Where(
				"group_id = ?",
				joinRequest.GroupId,
			).Update("group_cur_members", groupInfo.GroupCurMembers+1)

			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}
		}
	}
	// 提交事务
	tx.Commit()
	curUserNick := tools.GetUserNickFromCtx(ctx)

	//	发送通知消息
	requestUserNick, _ := server.GetUserInfo(joinRequest.UserId)
	strInfo := "'" + curUserNick + "' 同意了 ’" + requestUserNick + " ' 加入群聊"
	if !req.Approved {
		strInfo = "'" + curUserNick + "' 拒绝了 ’" + requestUserNick + " ' 加入群聊"
	}
	go func(_strGroupId, _strInfo string) {
		_ = s.groupInfoMsgPush(_strGroupId, strInfo, consts.GroupInfo_MemberRequest)
	}(joinRequest.GroupId, strInfo)
	// 返回结果
	res = &chat.HandleJoinRequestRes{
		Success: true,
	}

	return
}

// GroupMemberInfo 群成员信息结构体
type GroupMemberInfo struct {
	UserId     string `json:"user_id" dc:"群成员用户ID"`
	UserNick   string `json:"user_nick" dc:"群成员昵称"`
	UserAvatar string `json:"user_avatar" dc:"群成员头像"`
	MemberNick string `json:"member_nick" dc:"成员在群内的昵称"`
}

// GetGroupMemberInfo 获取某个群的成员信息，可以过滤指定用户
func GetGroupMemberInfo(groupId string, filterUserId string) (memberList []GroupMemberInfo) {
	// 1、参数校验
	if groupId == "" {
		return make([]GroupMemberInfo, 0)
	}

	// 2、初始化返回结果
	memberList = make([]GroupMemberInfo, 0)

	// 3、构建查询条件 - 性能优化：使用联表查询，一次性获取所有需要的数据
	query := dao.Db.Table("chat_group_members cgm").
		Select(`cgm.user_id, 	cgm.member_nick,
				ui.user_nick, ui.user_avatar`).
		Joins("LEFT JOIN user_infos ui ON cgm.user_id = ui.user_id").
		Where("cgm.group_id = ? AND cgm.deleted_at IS NULL AND cgm.exit_time IS NULL", groupId)

	// 4、如果需要过滤指定用户，添加过滤条件
	if filterUserId != "" {
		query = query.Where("cgm.user_id != ?", filterUserId)
	}

	// 5、按角色和加入时间排序：群主 > 管理员 > 普通成员，同角色按加入时间排序
	query = query.Order("cgm.member_role DESC, cgm.join_time ASC")

	// 7、执行查询
	if err := query.Find(&memberList).Error; err != nil {
		g.Log().Errorf(context.Background(), "查询群成员信息失败: %v", err)
		return make([]GroupMemberInfo, 0)
	}

	return memberList
}

// proupInfoMsgPush 好友添加成功消息推送与消息入库
//
//	1加群申请 (发送给管理员)， 2加群成功(发送给你所有成员)， 3，加群拒绝(发送给管理员)，4退群(发送给管理员)， 5 剔出群(发送给管理员)， 6 群主转让(所有成员) ,7 群解散(所有成员)
func (s *ServerGroup) groupInfoMsgPush(groupId string, groupInfoContent interface{}, groupInfoType string) (err error) {

	groupInfo := &WebSocketMessage{}
	groupInfo.MsgType = consts.MsgType_Chat
	groupInfo.MsgClientId = ""
	groupInfo.MsgReceiverType = consts.ReceiverType_Group
	groupInfo.MsgReceiverId = groupId
	groupInfo.MsgContentFmt = 1
	groupInfo.MsgContent = groupInfoContent

	switch groupInfoType {
	case consts.GroupInfo_GroupCreate: //	群创建 (发送给所有成员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_All, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberRequest: //	加群申请 (发送给管理员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_GroupAdmin, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberRequestOk: //	加群成功 (发送给所有成员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_All, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberRequestFailure: //	加群失败 (发送给管理员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_GroupAdmin, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberQuit: //	退群 (发送给管理员)
		{ //	退群 (发送给管理员)
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_GroupAdmin, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberRemove: //	剔出群 (发送给管理员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_GroupAdmin, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberRemoved: //	剔出群 (发送给被剔出者)
		{
			//	发送指定用户的，被剔出的消息
			groupInfo.MsgContentType = groupInfoType
			if strSlice, ok := groupInfoContent.([]string); ok {
				groupInfo.MsgReceiverTargets = strSlice
				groupInfo.MsgContent = "您已被移出群聊"
				_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_GroupCustom, consts.MsgType_System, uniqueId.GenerateMessageID())
			}
		}
	case consts.GroupInfo_AdminUpdate: //	群管理员变更 (发送给所有成员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_All, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_OwnerTransfer: //	群主转让 (发送给所有成员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_All, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberMute: //	群成员禁言 (发送给所有成员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_All, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_Disband: //	群解散 (发送给所有成员)
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_SystemOnly, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_GroupNameUpdate: //	群信息更新 (发送给所有成员)群名称更新
	case consts.GroupInfo_GroupAvatarUpdate: //	群信息更新 (发送给所有成员)群头像更新
	case consts.GroupInfo_GroupNoticeUpdate: //	群信息更新 (发送给所有成员)群公告更新
	case consts.GroupInfo_GroupAddTypeUpdate: //	群信息更新 (发送给所有成员)群加入方式更新
	case consts.GroupInfo_GroupDescUpdate: //	群信息更新 (发送给所有成员)群描述更新
		{
			groupInfo.MsgContentType = groupInfoType
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_All, consts.MsgType_System, uniqueId.GenerateMessageID())
		}
	case consts.GroupInfo_MemberNickUpdate: //	群成员昵称修改,我在本群的昵称,
		{
			groupInfo.MsgContentType = groupInfoType
			groupInfo.MsgType = consts.MsgType_ChatNotice
			_ = s.chatManager.PushServerMsgInfo(groupInfo, groupId, consts.PushStrategy_All, consts.MsgType_System, "")
		}
	}

	return nil
}
