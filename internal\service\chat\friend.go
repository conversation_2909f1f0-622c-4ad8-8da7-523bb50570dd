/*
******		FileName	:	friend.go
******		Describe	:	此文件主要用于好友管理的服务层实现
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   好友管理服务层
 */

package server

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	modelChat "ayj_chat_back/internal/model/chat"
	modelUser "ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/tools"
	"ayj_chat_back/internal/public/uniqueId"
	server "ayj_chat_back/internal/service/user"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// ServerFriend 好友管理服务结构体
type ServerFriend struct {
	chatManager ServerChat // 聊天长链接
	messageDb   ServerMessage
}

// GetFriendList 1、获取好友列表
func (s *ServerFriend) GetFriendList(req *chat.GetFriendListReq, userId string) (res *chat.GetFriendListRes, err error) {
	// 1. 使用联表查询一次性获取好友关系和用户信息，提升性能
	type FriendWithUserInfo struct {
		modelChat.FriendRelation
		UserNick   string `gorm:"column:user_nick"`
		UserAvatar string `gorm:"column:user_avatar"`
	}

	// 2. 构建查询条件，使用表别名避免字段歧义
	query := dao.Db.Table("friend_relations fr").
		Select(`
			fr.*,
			ui.user_nick,
			ui.user_avatar
		`).
		Joins("LEFT JOIN user_infos ui ON fr.friend_id = ui.user_id AND ui.deleted_at IS NULL").
		Where("fr.user_id = ? AND fr.deleted_at IS NULL", userId)

	// 3. 添加星标筛选条件
	if req.OnlyHasStar {
		query = query.Where("fr.is_star = ?", true)
	}

	var friendsWithInfo []FriendWithUserInfo
	result := query.Find(&friendsWithInfo)

	if result.Error != nil {
		return nil, result.Error
	}

	// 4. 处理未带标签的好友筛选（需要额外查询）
	if req.NotHasLabel {
		// 获取所有带标签的好友ID
		var labeledFriendIds []string
		labelQuery := dao.Db.Model(&modelChat.FriendLabelRelation{}).
			Where("user_id = ? AND deleted_at IS NULL", userId).
			Distinct("friend_id").
			Pluck("friend_id", &labeledFriendIds)

		if labelQuery.Error != nil && labelQuery.Error != gorm.ErrRecordNotFound {
			return nil, labelQuery.Error
		}

		// 创建带标签好友ID的映射，用于快速查找
		labeledFriendMap := make(map[string]bool, len(labeledFriendIds))
		for _, id := range labeledFriendIds {
			labeledFriendMap[id] = true
		}

		// 过滤掉带标签的好友
		filteredFriends := make([]FriendWithUserInfo, 0, len(friendsWithInfo))
		for _, friend := range friendsWithInfo {
			if !labeledFriendMap[friend.FriendId] {
				filteredFriends = append(filteredFriends, friend)
			}
		}
		friendsWithInfo = filteredFriends
	}

	// 5. 构建响应数据
	friendList := make([]chat.FriendInfo, 0, len(friendsWithInfo))
	for _, friend := range friendsWithInfo {
		friendInfo := chat.FriendInfo{
			UserId:         friend.FriendId,
			UserNick:       friend.UserNick,
			UserAvatar:     friend.UserAvatar,
			Remark:         friend.Remark,
			RelationStatus: friend.RelationStatus,
			IsStar:         friend.IsStar,
			IsTop:          friend.IsTop,
			MessageMute:    friend.MessageMute,
			CreatedAt:      tools.GtimeToStringNMD(friend.CreatedAt),
		}
		friendList = append(friendList, friendInfo)
	}

	// 6. 返回结果
	res = &chat.GetFriendListRes{
		List:       friendList,
		TotalCount: len(friendList),
	}
	return
}

// AddFriend 2、添加好友
func (s *ServerFriend) AddFriend(req *chat.AddFriendReq, userId string) (res *chat.AddFriendRes, err error) {
	// 1、参数校验
	if req.UserId == "" {
		return nil, errors.New("用户ID不能为空")
	}

	// 2、不能添加自己为好友
	if req.UserId == userId {
		return nil, errors.New("不能添加自己为好友")
	}

	// 3、检查目标用户是否存在并获取用户信息（包含好友验证设置）
	var targetUser modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).Where("user_id = ? AND deleted_at IS NULL", req.UserId).First(&targetUser)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	}

	// 4、检查目标用户是否在黑名单中
	var blackCount int64
	result = dao.Db.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND blocked_id = ? AND deleted_at IS NULL",
		req.UserId, userId,
	).Count(&blackCount)
	if result.Error != nil {
		return nil, result.Error
	}
	if blackCount > 0 {
		return nil, errors.New("您已被对方拉黑，无法添加好友")
	}

	// 5、检查是否已经是好友 - 使用 Unscoped() 忽略软删除，以便检查所有记录
	var existRelation modelChat.FriendRelation
	resultExist := dao.Db.Unscoped().Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		userId, req.UserId,
	).First(&existRelation)

	// 6、检查对方是否已删除 - 使用 Unscoped() 忽略软删除，以便检查所有记录
	var partyRelation modelChat.FriendRelation
	resultParty := dao.Db.Unscoped().Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		req.UserId, userId,
	).First(&partyRelation)

	// 7、检查好友关系状态并处理
	if resultExist.Error == nil && resultParty.Error == nil {
		// 两条记录都存在（无论是否被软删除）
		if existRelation.DeletedAt == nil && partyRelation.DeletedAt == nil {
			// 双向好友关系都存在且未被删除，已经是好友
			return &chat.AddFriendRes{
				AddStatus: 2,
				Message:   "该用户已经是您的好友, 无需添加",
			}, nil
		} else if existRelation.DeletedAt != nil && partyRelation.DeletedAt == nil {
			// 自己删除了对方，但对方没有删除自己
			// 根据对方的好友验证设置决定是否直接恢复关系
			if !targetUser.FriendNeedVerify {
				// 对方不需要验证，直接恢复好友关系
				now := gtime.Now()
				result = dao.Db.Unscoped().Model(&modelChat.FriendRelation{}).Where(
					"user_id = ? AND friend_id = ?",
					userId, req.UserId,
				).Updates(map[string]interface{}{
					"deleted_at": nil,
					"updated_at": now,
					"created_at": now,
				})

				if result.Error != nil {
					return nil, result.Error
				}
				return &chat.AddFriendRes{
					AddStatus: 3,
					Message:   "已成功恢复好友关系",
				}, nil
			}
			// 对方需要验证，继续执行申请流程
		} else if existRelation.DeletedAt == nil && partyRelation.DeletedAt != nil {
			// 自己没删除对方，但对方删除了自己，需要重新申请
			// 继续执行后面的申请流程
		} else {
			// 双方都删除了对方，需要重新申请
			// 继续执行后面的申请流程
		}
	} else if resultExist.Error != gorm.ErrRecordNotFound || resultParty.Error != gorm.ErrRecordNotFound {
		// 如果错误不是"记录不存在"，则返回错误
		if resultExist.Error != gorm.ErrRecordNotFound {
			return nil, resultExist.Error
		}
		return nil, resultParty.Error
	}

	// 8、检查目标用户的好友验证设置
	if !targetUser.FriendNeedVerify {
		// 8.1 对方不需要验证，直接建立好友关系
		return s.createDirectFriendship(userId, req.UserId, req.RequestSource)
	} else {
		// 8.2 对方需要验证，继续执行申请流程
		return s.handelAddFriendRequest(req, userId)
	}
}

// GetFriendInfo 获取好友信息
// 首先在 UserInfo 表中获取基本信息，然后在好友关系表 FriendRelation 获取相关信息，
// 最后在黑名单表获取信息 FriendBlackList，如果好友关系表与黑名单表都没有信息，就用默认数据，表明当前好友未添加成好友
func (s *ServerFriend) GetFriendInfo(req *chat.GetFriendInfoReq, userId string) (res *chat.GetFriendInfoRes, err error) {
	// 参数校验
	if req.FriendId == "" {
		return nil, errors.New("好友ID不能为空")
	}

	//	初始化结构
	res = &chat.GetFriendInfoRes{}

	// 1. 从 UserInfo 表获取基本信息
	var userInfo modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(
		"user_id = ? AND deleted_at IS NULL",
		req.FriendId,
	).First(&userInfo)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	} else {
		// 填充用户基本信息
		res.UserId = userInfo.UserId
		res.UserPhone = userInfo.UserPhone
		res.UserAvatar = userInfo.UserAvatar
		res.UserNick = userInfo.UserNick
		res.UserSex = userInfo.UserSex
		res.UserRoleType = userInfo.UserRoleType
		res.UserBirth = userInfo.UserBirth
		res.UserEmail = userInfo.UserEmail
		res.UserSignature = userInfo.UserSignature
		res.UserStatus = userInfo.UserStatus
		res.UserRegion = userInfo.UserRegion
	}

	// 2. 从 FriendRelation 表获取好友关系信息
	var friendRelation modelChat.FriendRelation
	result = dao.Db.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ? AND deleted_at IS NULL",
		userId, req.FriendId,
	).First(&friendRelation)

	// 如果存在好友关系，填充好友关系信息
	if result.Error == nil {
		res.Remark = friendRelation.Remark
		res.IsStar = friendRelation.IsStar
		res.IsTop = friendRelation.IsTop
		res.MessageMute = friendRelation.MessageMute
		res.RelationStatus = friendRelation.RelationStatus
		res.AddTime = tools.GtimeToStringNMD(friendRelation.CreatedAt)

		// 3. 从 FriendRequest 表获取添加好友的信息 好友添加方式
		// 首先查询是否有当前用户发送给对方的申请
		var sentRequest modelChat.FriendRequest
		resultSent := dao.Db.Model(&modelChat.FriendRequest{}).Where(
			"sender_id = ? AND receiver_id = ? AND deleted_at IS NULL",
			userId, req.FriendId,
		).Order("created_at DESC").First(&sentRequest)

		// 然后查询是否有对方发送给当前用户的申请
		var receivedRequest modelChat.FriendRequest
		resultReceived := dao.Db.Model(&modelChat.FriendRequest{}).Where(
			"sender_id = ? AND receiver_id = ? AND deleted_at IS NULL",
			req.FriendId, userId,
		).Order("created_at DESC").First(&receivedRequest)

		// 处理查询结果，优先使用最新的申请记录
		if resultSent.Error == nil && resultReceived.Error == nil {
			// 两种申请都存在，使用最新的一条
			if sentRequest.CreatedAt.After(receivedRequest.CreatedAt) {
				res.RequestType = 2 // 我加的对方
				res.RequestSource = sentRequest.RequestSource
			} else {
				res.RequestType = 1 // 对方加的我
				res.RequestSource = receivedRequest.RequestSource
			}
		} else if resultSent.Error == nil {
			// 只有我发送的申请
			res.RequestType = 2 // 我加的对方
			res.RequestSource = sentRequest.RequestSource
		} else if resultReceived.Error == nil {
			// 只有对方发送的申请
			res.RequestType = 1 // 对方加的我
			res.RequestSource = receivedRequest.RequestSource
		} else {
			// 没有申请记录，设置默认值
			res.RequestType = 0 // 无申请记录
			res.RequestSource = 0
		}

		//	5、获取好友标签信息
		// 首先获取该好友关联的所有标签ID
		var labelIds []string // 修改为字符串类型，因为label_id是字符串
		result = dao.Db.Model(&modelChat.FriendLabelRelation{}).
			Where("user_id = ? AND friend_id = ? AND deleted_at IS NULL", userId, req.FriendId). // 添加软删除条件
			Pluck("label_id", &labelIds)

		if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
			return nil, result.Error
		}

		// 如果有关联的标签
		if len(labelIds) > 0 {
			// 批量查询标签详细信息
			var labels []modelChat.FriendLabel
			result = dao.Db.Model(&modelChat.FriendLabel{}).
				Where("label_id IN ? AND user_id = ? AND deleted_at IS NULL", labelIds, userId).
				Order("label_order ASC").
				Find(&labels)

			if result.Error != nil {
				return nil, result.Error
			}

			// 构建标签列表，预分配容量以提高性能
			labelList := make([]chat.FriendLabelBasicInfo, 0, len(labels))

			for _, label := range labels {
				labelInfo := chat.FriendLabelBasicInfo{
					LabelId:     label.LabelId,
					LabelName:   label.LabelName,
					LabelOrder:  label.LabelOrder,
					FriendCount: label.FriendCount,
				}
				labelList = append(labelList, labelInfo)
			}

			// 设置标签列表
			res.LabelList = labelList
		} else {
			// 如果没有关联的标签，设置为空数组而不是nil
			res.LabelList = []chat.FriendLabelBasicInfo{}
		}
	}

	// 5. 从 FriendBlackList 表获取黑名单信息
	var blackList modelChat.FriendBlackList
	result = dao.Db.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND blocked_id = ? AND deleted_at IS NULL",
		userId, req.FriendId,
	).First(&blackList)

	// 如果存在黑名单记录，填充黑名单信息
	if result.Error == nil {
		res.IsBlocked = true
		res.BlockReason = blackList.BlockReason
	}

	return res, nil
}

// HandleFriendRequest 处理好友申请
func (s *ServerFriend) HandleFriendRequest(req *chat.HandleFriendRequestReq, userId string) (res *chat.HandleFriendRequestRes, err error) {
	// 参数校验
	if req.RequestId <= 0 {
		return nil, errors.New("申请记录ID不能为空")
	}

	// 获取申请记录
	var friendRequest modelChat.FriendRequest
	result := dao.Db.Model(&modelChat.FriendRequest{}).Where(
		"id = ?",
		req.RequestId,
	).First(&friendRequest)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("申请记录不存在")
		}
		return nil, result.Error
	}

	// 检查是否是申请的接收者
	if friendRequest.ReceiverId != userId {
		return nil, errors.New("您发出的好友申请,对方才有权处理")
	}

	// 检查申请是否已处理
	if friendRequest.HandleStatus > consts.FriendHandlrStatus_Pending {
		return nil, errors.New("该申请已处理")
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 更新申请状态
	now := gtime.Now()
	handleTime := gtime.Now()
	result = tx.Model(&modelChat.FriendRequest{}).Where(
		"id = ?",
		req.RequestId,
	).Updates(map[string]interface{}{
		"handle_status": req.HandleStatus,
		"handle_msg":    req.HandleMsg,
		"handle_time":   handleTime,
		"updated_at":    now,
	})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果同意申请，添加好友关系
	if req.HandleStatus == consts.FriendHandlrStatus_agree {
		// 检查是否已经是好友
		var existRelation modelChat.FriendRelation
		result = tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
			"user_id = ? AND friend_id = ?",
			userId, friendRequest.SenderId,
		).First(&existRelation)

		if result.Error == gorm.ErrRecordNotFound {
			// 创建双向好友关系
			// 1. 接收者 -> 发送者
			receiverRelation := &modelChat.FriendRelation{
				UserId:         userId,
				FriendId:       friendRequest.SenderId,
				RelationStatus: 1, // 正常
				IsStar:         false,
				IsTop:          false,
				ShardKey:       int(time.Now().Unix() % 10), // 简单实现
				CreatedAt:      now,
				UpdatedAt:      now,
			}

			result = tx.Create(receiverRelation)
			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}

			// 2. 发送者 -> 接收者
			senderRelation := &modelChat.FriendRelation{
				UserId:         friendRequest.SenderId,
				FriendId:       userId,
				RelationStatus: 1, // 正常
				IsStar:         false,
				IsTop:          false,
				ShardKey:       int(time.Now().Unix() % 10), // 简单实现
				CreatedAt:      now,
				UpdatedAt:      now,
			}

			result = tx.Create(senderRelation)
			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}

		} else if result.Error != nil {
			// 其他错误
			tx.Rollback()
			return nil, result.Error
		} else {
			// 如果已经是好友，更新
			result = tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
				"user_id = ? AND friend_id = ?",
				userId, friendRequest.SenderId,
			).Update("deleted_at", nil)

			result = tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
				"user_id = ? AND friend_id = ?",
				friendRequest.SenderId, userId,
			).Update("deleted_at", nil)
		}
	}

	// 提交事务
	tx.Commit()

	// 如果同意申请，发送好友添加成功消息
	if req.HandleStatus == consts.FriendHandlrStatus_agree {
		go func() {
			// 异步发送消息，避免影响主流程
			if err := s.addFriendOkMsgPush(friendRequest.SenderId, friendRequest.RequestMsg, userId); err != nil {
				g.Log().Errorf(context.Background(), "发送好友添加成功消息失败: %v", err)
			}
		}()
	}

	// 返回结果
	res = &chat.HandleFriendRequestRes{
		Success: true,
		Message: "处理成功",
	}

	return
}

// GetFriendRequests 获取好友申请列表
func (s *ServerFriend) GetFriendRequests(req *chat.GetFriendRequestsReq, userId string) (res *chat.GetFriendRequestsRes, err error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 100
	}

	// 初始化查询
	query := dao.Db.Model(&modelChat.FriendRequest{}).Where("deleted_at IS NULL")

	// 根据申请方向筛选，并考虑 SenderDelShow 和 ReceiverDelShow 字段
	switch req.RequestType {
	case 1: // 收到的申请
		query = query.Where("receiver_id = ? AND receiver_del_show = ?", userId, false)
	case 2: // 发出的申请
		query = query.Where("sender_id = ? AND sender_del_show = ?", userId, false)
	default: // 全部申请
		query = query.Where(
			"(receiver_id = ? AND receiver_del_show != ?) OR (sender_id = ? AND sender_del_show != ?)",
			userId, true, userId, true,
		)
	}

	// 根据状态筛选
	if req.Status > 0 {
		query = query.Where("handle_status = ?", req.Status)
	}

	// 获取申请总数
	var total int64
	result := query.Count(&total)
	if result.Error != nil {
		return nil, result.Error
	}

	// 获取申请列表
	var requests []modelChat.FriendRequest
	result = query.Order("created_at DESC").
		Offset((req.Page - 1) * req.Size).
		Limit(req.Size).
		Find(&requests)

	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	var requestList []chat.FriendRequestInfo
	for _, request := range requests {
		// 构建申请信息
		requestInfo := chat.FriendRequestInfo{
			RequestId:     int(request.Id),
			RequestMsg:    request.RequestMsg,
			RequestSource: request.RequestSource,
			HandleStatus:  request.HandleStatus,
			HandleMsg:     request.HandleMsg,
			HandleTime:    tools.GtimeToStringNMDHMS(request.HandleTime),
			CreatedAt:     tools.GtimeToStringNMDHMS(request.CreatedAt),
		}

		// 确定申请方向
		if request.ReceiverId == userId {
			// 获取申请者用户信息
			senderNick, senderAvatar := s.getUserInfo(request.SenderId)

			requestInfo.FriendId = request.SenderId
			requestInfo.FriendNick = senderNick
			requestInfo.FriendAvatar = senderAvatar
			requestInfo.RequestType = 1 // 收到的申请

		} else {
			// 获取接收者用户信息
			receiverNick, receiverAvatar := s.getUserInfo(request.ReceiverId)

			requestInfo.FriendId = request.ReceiverId
			requestInfo.FriendNick = receiverNick
			requestInfo.FriendAvatar = receiverAvatar
			requestInfo.RequestType = 2 // 收到的申请
		}

		requestList = append(requestList, requestInfo)
	}

	// 返回结果
	res = &chat.GetFriendRequestsRes{
		List:       requestList,
		TotalCount: int(total),
	}

	return
}

// DelFriendRequestShow 删除好友请求记录显示
func (s *ServerFriend) DelFriendRequestShow(req *chat.FriendRequestDelShowReq, userId string) (res *chat.FriendRequestDelShowRes, err error) {
	// 参数校验
	if req.RequestId <= 0 {
		return nil, errors.New("请求ID不能为空")
	}

	// 查询好友请求记录
	var friendRequest modelChat.FriendRequest
	result := dao.Db.Model(&modelChat.FriendRequest{}).Where(
		"id = ?",
		req.RequestId,
	).First(&friendRequest)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("请求记录不存在")
		}
		return nil, result.Error
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 根据用户身份更新相应的字段
	updates := make(map[string]interface{})
	updates["updated_at"] = time.Now()

	if friendRequest.SenderId == userId {
		// 如果当前用户是发送者，更新 SenderDelShow 字段
		updates["sender_del_show"] = true
	} else if friendRequest.ReceiverId == userId {
		// 如果当前用户是接收者，更新 ReceiverDelShow 字段
		updates["receiver_del_show"] = true
	} else {
		// 如果当前用户既不是发送者也不是接收者，返回错误
		return nil, errors.New("您无权操作此记录")
	}

	// 更新记录
	result = tx.Model(&modelChat.FriendRequest{}).Where(
		"id = ?",
		req.RequestId,
	).Updates(updates)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	tx.Commit()

	// 返回结果
	res = &chat.FriendRequestDelShowRes{
		RequestId: true,
		Success:   true,
	}

	return
}

// DeleteFriend 删除好友
func (s *ServerFriend) DeleteFriend(req *chat.DeleteFriendReq, userId string) (res *chat.DeleteFriendRes, err error) {
	// 参数校验
	if req.FriendId == "" {
		return nil, errors.New("好友ID不能为空")
	}
	if req.FriendId == userId {
		return nil, errors.New("您的好友id是您自己的id, 不可删除自己")
	}
	// 检查是否是好友关系
	var relation modelChat.FriendRelation
	result := dao.Db.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ? AND deleted_at IS NULL",
		userId, req.FriendId,
	).First(&relation)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("该用户不是您的好友")
		}
		return nil, result.Error
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 1. 删除好友关系（单向删除）
	now := time.Now()

	// 删除自己与好友的关系
	result = tx.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		userId, req.FriendId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 是否需要 双向删除 ，删除好友与自己的关系
	if req.IsDeleteBoth {
		result = tx.Model(&modelChat.FriendRelation{}).Where(
			"user_id = ? AND friend_id = ?",
			req.FriendId, userId,
		).Update("deleted_at", now)

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 2. 删除好友标签关联
	result = tx.Model(&modelChat.FriendLabelRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		userId, req.FriendId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 3. 删除好友请求记录（如果存在）
	result = tx.Model(&modelChat.FriendRequest{}).Where(
		"(sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)",
		userId, req.FriendId, req.FriendId, userId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	tx.Commit()

	// 返回结果
	res = &chat.DeleteFriendRes{
		Success: true,
		Message: "删除好友成功",
	}

	return
}

// UpdateFriendInfo 更新好友信息
func (s *ServerFriend) UpdateFriendInfo(ctx context.Context, req *chat.UpdateFriendInfoReq, userId string) (res *chat.UpdateFriendInfoRes, err error) {
	// 参数校验
	if req.FriendId == "" {
		return nil, errors.New("好友ID不能为空")
	}

	// 检查是否是好友关系
	var relation modelChat.FriendRelation
	result := dao.Db.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ? AND deleted_at IS NULL",
		userId, req.FriendId,
	).First(&relation)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("该用户不是您的好友")
		}
		return nil, result.Error
	}

	// 更新字段
	updates := make(map[string]interface{})

	// 获取原始请求对象， 判断用户是否传输了这个数据
	request := g.RequestFromCtx(ctx)
	requestMap := request.GetMap()
	// 4.1 是否更新好友备注
	if _, ok := requestMap["remark"]; ok {
		updates["remark"] = req.Remark
	}
	// 4.2 是否更新好友关系
	if _, ok := requestMap["relation_status"]; ok {
		updates["relation_status"] = req.RelationStatus
	}
	// 4.3 更新是否标星
	if _, ok := requestMap["is_star"]; ok {
		updates["is_star"] = req.IsStar
	}
	// 4.4 更新是否顶置
	if _, ok := requestMap["is_top"]; ok {
		updates["is_top"] = req.IsTop
	}
	// 4.5 更新是否消息免打扰
	if _, ok := requestMap["message_mute"]; ok {
		updates["message_mute"] = req.MessageMute
	}

	// 如果没有更新字段，直接返回成功
	if len(updates) <= 0 {
		return nil, errors.New("无更新内容")
	}

	// 5. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 6. 更新好友信息
	updates["updated_at"] = time.Now()
	result = tx.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		userId, req.FriendId,
	).Updates(updates)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 7. 提交事务
	tx.Commit()

	// 8. 获取更新后的成员信息
	result = dao.Db.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ? AND deleted_at IS NULL",
		userId, req.FriendId,
	).First(&relation)

	if result.Error != nil {
		return nil, result.Error
	}
	// 9. 返回结果
	res = &chat.UpdateFriendInfoRes{
		Remark:         relation.Remark,
		RelationStatus: relation.RelationStatus,
		IsStar:         relation.IsStar,
		IsTop:          relation.IsTop,
	}

	return
}

// 9、CreateFriendLabel 创建好友标签 , 自动设置标签排序值为当前最大排序值+1
func (s *ServerFriend) CreateFriendLabel(req *chat.CreateFriendLabelReq, userId string) (res *chat.CreateFriendLabelRes, err error) {
	// 1. 参数校验
	if req.LabelName == "" {
		return nil, errors.New("标签名称不能为空")
	}

	// 1.1 标签名称长度限制
	if len(req.LabelName) > 20 {
		return nil, errors.New("标签名称长度不能超过20个字符")
	}

	// 2. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 3. 检查用户当前的标签总数
	var currentLabelCount int64
	result := tx.Model(&modelChat.FriendLabel{}).Where(
		"user_id = ? AND deleted_at IS NULL",
		userId,
	).Count(&currentLabelCount)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 3.1 检查是否超过最大标签数量限制
	if int(currentLabelCount) >= 100 {
		tx.Rollback()
		return nil, errors.New("标签总数不能超过100个")
	}

	// 4. 检查标签名称是否已存在
	var count int64
	result = tx.Model(&modelChat.FriendLabel{}).Where(
		"user_id = ? AND label_name = ? AND deleted_at IS NULL",
		userId, req.LabelName,
	).Count(&count)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	if count > 0 {
		tx.Rollback()
		return nil, errors.New("标签名称 '" + req.LabelName + "' 已存在")
	}

	// 5. 获取当前最大排序值
	// 5.1 性能优化: 使用COALESCE函数处理NULL值，避免额外的判断
	var maxOrder struct {
		MaxOrder int
	}
	result = tx.Model(&modelChat.FriendLabel{}).
		Select("COALESCE(MAX(label_order), 0) as max_order").
		Where("user_id = ? AND deleted_at IS NULL", userId).
		Scan(&maxOrder)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 5.2 设置新标签的排序值为当前最大值+1
	newOrder := maxOrder.MaxOrder + 1

	// 6. 创建标签
	now := gtime.Now()
	label := &modelChat.FriendLabel{
		UserId:     userId,
		LabelId:    uniqueId.GenerateLabelID(),
		LabelName:  req.LabelName,
		LabelOrder: newOrder, // 设置为最大排序值+1
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	// 6.2 创建标签记录
	result = tx.Create(label)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 7. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 8. 返回结果
	res = &chat.CreateFriendLabelRes{
		LabelId:    label.LabelId,
		LabelName:  label.LabelName,
		LabelOrder: label.LabelOrder,
	}

	return
}

// UpdateFriendLabel 更新好友标签
func (s *ServerFriend) UpdateFriendLabel(req *chat.UpdateFriendLabelReq, userId string) (res *chat.UpdateFriendLabelRes, err error) {
	// 参数校验
	if req.LabelId == "" {
		return nil, errors.New("标签ID不能为空")
	}

	// 检查标签是否存在
	var label modelChat.FriendLabel
	result := dao.Db.Model(&modelChat.FriendLabel{}).Where(
		"label_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.LabelId, userId,
	).First(&label)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("标签不存在")
		}
		return nil, result.Error
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.LabelName != "" && req.LabelName != label.LabelName {
		// 检查标签名称是否已存在
		var count int64
		result := dao.Db.Model(&modelChat.FriendLabel{}).Where(
			"user_id = ? AND label_name = ? AND label_id != ? AND deleted_at IS NULL",
			userId, req.LabelName, req.LabelId,
		).Count(&count)

		if result.Error != nil {
			return nil, result.Error
		}

		if count > 0 {
			return nil, errors.New("标签名称 '" + req.LabelName + "' 已存在")
		}

		updates["label_name"] = req.LabelName
	}

	// 如果没有更新字段，直接返回成功
	if len(updates) == 0 {
		return &chat.UpdateFriendLabelRes{
			LabelId:   label.LabelId,
			LabelName: label.LabelName,
		}, nil
	}

	// 更新标签
	updates["updated_at"] = time.Now()
	result = dao.Db.Model(&modelChat.FriendLabel{}).Where(
		"label_id = ?",
		req.LabelId,
	).Updates(updates)

	if result.Error != nil {
		return nil, result.Error
	}

	// 重新获取标签信息
	dao.Db.Model(&modelChat.FriendLabel{}).Where(
		"label_id = ?",
		req.LabelId,
	).First(&label)

	// 返回结果
	res = &chat.UpdateFriendLabelRes{
		LabelId:   label.LabelId,
		LabelName: label.LabelName,
	}

	return
}

// 11、GetFriendLabels 获取好友标签列表  如果 LabelId > 0，获取指定标签下的好友列表 ; 如果 LabelId <= 0，获取所有标签列表
func (s *ServerFriend) GetFriendLabels(req *chat.GetFriendLabelsReq, userId string) (res *chat.GetFriendLabelsRes, err error) {
	// 初始化响应
	res = &chat.GetFriendLabelsRes{
		List: []chat.FriendLabelInfo{},
	}
	// 根据 LabelId 参数决定查询逻辑
	if req.LabelId != "" {
		// 获取指定标签下的好友列表
		return s.getSpecificLabelWithFriends(req.LabelId, userId)
	} else {
		// 获取所有标签列表
		return s.getAllLabels(req, userId)
	}
}

// getSpecificLabelWithFriends 获取指定标签及其下的好友列表
func (s *ServerFriend) getSpecificLabelWithFriends(labelId string, userId string) (*chat.GetFriendLabelsRes, error) {
	// 1. 查询指定标签
	var label modelChat.FriendLabel
	result := dao.Db.Model(&modelChat.FriendLabel{}).Where(
		"label_id = ? AND user_id = ? AND deleted_at IS NULL",
		labelId, userId,
	).First(&label)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return &chat.GetFriendLabelsRes{
				List: []chat.FriendLabelInfo{},
			}, nil
		}
		return nil, result.Error
	}

	// 2. 获取标签下的好友数量
	var friendCount int64
	result = dao.Db.Model(&modelChat.FriendLabelRelation{}).Where(
		"user_id = ? AND label_id = ?",
		userId, labelId,
	).Count(&friendCount)

	if result.Error != nil {
		return nil, result.Error
	}

	// 3. 获取标签下的好友列表
	friendMap, err := s.getLabelFriendsMap(userId, []string{labelId})
	if err != nil {
		return nil, err
	}

	// 4. 构建标签信息
	labelInfo := chat.FriendLabelInfo{
		LabelInfo: chat.FriendLabelBasicInfo{
			LabelId:     label.LabelId,
			LabelName:   label.LabelName,
			LabelOrder:  label.LabelOrder,
			FriendCount: int(friendCount),
		},
		FriendList: []chat.FriendInfo{}, // 默认为空数组
	}

	// 添加好友列表
	if friendList, exists := friendMap[labelId]; exists && len(friendList) > 0 {
		labelInfo.FriendList = friendList
	}

	// 返回结果
	return &chat.GetFriendLabelsRes{
		List: []chat.FriendLabelInfo{labelInfo},
	}, nil
}

// getAllLabels 获取所有标签列表
func (s *ServerFriend) getAllLabels(req *chat.GetFriendLabelsReq, userId string) (*chat.GetFriendLabelsRes, error) {
	// 1. 获取标签列表
	var labels []modelChat.FriendLabel
	result := dao.Db.Model(&modelChat.FriendLabel{}).Where(
		"user_id = ? AND deleted_at IS NULL",
		userId,
	).Order("label_order ASC").Find(&labels)

	if result.Error != nil {
		return nil, result.Error
	}

	// 如果没有标签，直接返回空列表
	if len(labels) == 0 {
		return &chat.GetFriendLabelsRes{
			List: []chat.FriendLabelInfo{},
		}, nil
	}

	// 2. 提取所有标签ID并创建映射
	var labelIds []string
	labelMap := make(map[string]*modelChat.FriendLabel, len(labels))
	for i := range labels {
		labelIds = append(labelIds, labels[i].LabelId)
		labelMap[labels[i].LabelId] = &labels[i]
	}

	// 3. 性能优化：批量获取所有标签的好友数量
	labelFriendCountMap := make(map[int64]int, len(labelIds))
	var labelFriendCounts []struct {
		LabelId int64
		Count   int64
	}

	countQuery := dao.Db.Model(&modelChat.FriendLabelRelation{}).
		Select("id, COUNT(*) as count").
		Where("user_id = ? AND label_id IN ?", userId, labelIds).
		Group("id")

	// 注意：这里假设我们使用的是硬删除，如果使用软删除，需要添加 AND deleted_at IS NULL 条件
	result = countQuery.Find(&labelFriendCounts)
	if result.Error != nil {
		return nil, result.Error
	}

	// 将结果转换为map
	for _, item := range labelFriendCounts {
		labelFriendCountMap[item.LabelId] = int(item.Count)
	}

	// 4. 如果需要获取好友列表，批量获取所有标签的好友
	var friendMap map[string][]chat.FriendInfo
	var err error
	if req.IsNeedFriendList {
		friendMap, err = s.getLabelFriendsMap(userId, labelIds)
		if err != nil {
			return nil, err
		}
	}

	// 5. 构建标签信息列表
	labelList := make([]chat.FriendLabelInfo, 0, len(labels))
	for _, label := range labels {
		// 获取标签关联的好友数量（从map中获取，避免多次查询）
		friendCount, exists := labelFriendCountMap[label.Id]
		if !exists {
			friendCount = 0
		}

		// 构建标签信息
		labelInfo := chat.FriendLabelInfo{
			LabelInfo: chat.FriendLabelBasicInfo{
				LabelId:     label.LabelId,
				LabelName:   label.LabelName,
				LabelOrder:  label.LabelOrder,
				FriendCount: friendCount,
			},
			FriendList: []chat.FriendInfo{}, // 始终初始化为空切片，避免JSON序列化为null
		}

		// 如果需要获取好友列表，添加好友列表
		if req.IsNeedFriendList {
			if friendList, exists := friendMap[label.LabelId]; exists && len(friendList) > 0 {
				labelInfo.FriendList = friendList
			}
		}

		labelList = append(labelList, labelInfo)
	}

	// 6、返回结果
	return &chat.GetFriendLabelsRes{
		List: labelList,
	}, nil
}

// getLabelFriendsMap 批量获取多个标签下的好友列表
func (s *ServerFriend) getLabelFriendsMap(userId string, labelIds []string) (map[string][]chat.FriendInfo, error) {
	// 创建结果map，并为每个标签初始化一个空切片
	result := make(map[string][]chat.FriendInfo)

	// 为每个标签ID初始化一个空切片
	for _, labelId := range labelIds {
		result[labelId] = []chat.FriendInfo{}
	}

	// 如果没有标签，直接返回空map
	if len(labelIds) == 0 {
		return result, nil
	}

	// 1. 获取所有标签关联的好友ID
	type LabelFriendRelation struct {
		LabelId  string
		FriendId string
	}

	var relations []LabelFriendRelation

	// 查询所有标签关联的好友ID
	// 注意：这里假设我们使用的是硬删除，如果使用软删除，需要添加 AND deleted_at IS NULL 条件
	queryResult := dao.Db.Model(&modelChat.FriendLabelRelation{}).
		Select("label_id, friend_id").
		Where("user_id = ? AND label_id IN ?", userId, labelIds).
		Find(&relations)

	if queryResult.Error != nil {
		return nil, queryResult.Error
	}

	// 如果没有关联关系，直接返回空map
	if len(relations) == 0 {
		return result, nil
	}

	// 2. 收集所有好友ID
	friendIds := make(map[string]bool)
	labelFriendMap := make(map[string][]string)

	for _, relation := range relations {
		friendIds[relation.FriendId] = true

		// 将好友ID添加到对应标签的列表中
		labelFriendMap[relation.LabelId] = append(labelFriendMap[relation.LabelId], relation.FriendId)
	}

	// 将map转换为slice
	var uniqueFriendIds []string
	for id := range friendIds {
		uniqueFriendIds = append(uniqueFriendIds, id)
	}

	// 3. 批量获取所有好友信息
	var friendRelations []modelChat.FriendRelation
	queryResult = dao.Db.Model(&modelChat.FriendRelation{}).
		Where("user_id = ? AND friend_id IN ? AND deleted_at IS NULL", userId, uniqueFriendIds).
		Find(&friendRelations)

	if queryResult.Error != nil {
		return nil, queryResult.Error
	}

	// 创建好友ID到好友信息的映射
	friendInfoMap := make(map[string]modelChat.FriendRelation)
	for _, relation := range friendRelations {
		friendInfoMap[relation.FriendId] = relation
	}

	// 4. 批量获取所有好友的用户信息
	type UserBasicInfo struct {
		UserId     string
		UserNick   string
		UserAvatar string
	}

	var userInfos []UserBasicInfo
	queryResult = dao.Db.Model(&modelUser.UserInfo{}).
		Select("user_id, user_nick, user_avatar").
		Where("user_id IN ?", uniqueFriendIds).
		Find(&userInfos)

	if queryResult.Error != nil {
		return nil, queryResult.Error
	}

	// 创建用户ID到用户信息的映射
	userInfoMap := make(map[string]UserBasicInfo)
	for _, info := range userInfos {
		userInfoMap[info.UserId] = info
	}

	// 5. 构建每个标签的好友列表
	// 5.1 遍历所有请求的标签ID
	for _, labelId := range labelIds {
		// 5.2 获取当前标签关联的好友ID列表
		friendIdList, exists := labelFriendMap[labelId]
		if !exists {
			// 确保标签存在于结果中，即使没有关联的好友
			continue // 已经在初始化时为每个标签设置了空数组
		}

		// 5.3 预分配好友列表容量
		friendList := make([]chat.FriendInfo, 0, len(friendIdList))

		// 5.4 遍历当前标签关联的所有好友ID
		for _, friendId := range friendIdList {
			// 5.5 获取好友关系信息
			relation, relationExists := friendInfoMap[friendId]
			if !relationExists {
				continue // 跳过不存在的好友关系
			}

			// 5.6 获取用户基本信息
			userInfo, userExists := userInfoMap[friendId]
			if !userExists {
				continue // 跳过不存在的用户
			}

			// 5.7 构建好友信息
			friendInfo := chat.FriendInfo{
				UserId:         friendId,
				UserNick:       userInfo.UserNick,
				UserAvatar:     userInfo.UserAvatar,
				Remark:         relation.Remark,
				RelationStatus: relation.RelationStatus,
				IsStar:         relation.IsStar,
				IsTop:          relation.IsTop,
				MessageMute:    relation.MessageMute,
				CreatedAt:      tools.GtimeToStringNMDHMS(relation.CreatedAt),
			}

			// 5.8 添加到好友列表
			friendList = append(friendList, friendInfo)
		}

		// 5.9 将好友列表添加到结果map中
		result[labelId] = friendList
	}

	return result, nil
}

// DeleteFriendLabel 删除好友标签
// 使用硬删除方式，彻底删除标签及其关联
func (s *ServerFriend) DeleteFriendLabel(req *chat.DeleteFriendLabelReq, userId string) (res *chat.DeleteFriendLabelRes, err error) {
	// 1. 参数校验
	if len(req.LabelIds) == 0 {
		return nil, errors.New("标签ID列表不能为空")
	}

	// 2. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 3. 检查标签是否存在
	var count int64
	result := tx.Model(&modelChat.FriendLabel{}).Where(
		"label_id IN ? AND user_id = ? AND deleted_at IS NULL",
		req.LabelIds, userId,
	).Count(&count)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	if int(count) != len(req.LabelIds) {
		tx.Rollback()
		return nil, errors.New("部分标签不存在")
	}

	// 4. 硬删除标签关联
	result = tx.Unscoped().Where(
		"label_id IN ? AND user_id = ?",
		req.LabelIds, userId,
	).Delete(&modelChat.FriendLabelRelation{})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 5. 硬删除标签
	result = tx.Unscoped().Where(
		"label_id IN ? AND user_id = ?",
		req.LabelIds, userId,
	).Delete(&modelChat.FriendLabel{})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 6. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 7. 返回结果
	res = &chat.DeleteFriendLabelRes{
		LabelIds: req.LabelIds,
	}

	return
}

// RelateFriendLabel 为好友关联标签（覆盖模式）
func (s *ServerFriend) RelateFriendLabel(req *chat.RelateFriendLabelReq, userId string) (res *chat.RelateFriendLabelRes, err error) {
	// 参数校验 允许空标签列表，表示清空所有标签
	if req.FriendId == "" {
		return nil, errors.New("好友ID不能为空")
	}

	// 检查是否是好友关系
	var friendCount int64
	result := dao.Db.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ? AND deleted_at IS NULL",
		userId, req.FriendId,
	).Count(&friendCount)

	if result.Error != nil {
		return nil, result.Error
	}

	if friendCount == 0 {
		return nil, errors.New("该用户不是您的好友")
	}

	// 如果标签列表不为空，检查标签是否存在
	if len(req.LabelIds) > 0 {
		var labelCount int64
		result = dao.Db.Model(&modelChat.FriendLabel{}).Where(
			"label_id IN ? AND user_id = ? AND deleted_at IS NULL",
			req.LabelIds, userId,
		).Count(&labelCount)

		if result.Error != nil {
			return nil, result.Error
		}

		if int(labelCount) != len(req.LabelIds) {
			return nil, errors.New("部分标签不存在")
		}
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 1. 先硬删除该好友的所有现有标签关联
	gtimeNow := gtime.Now()

	// 获取要删除的标签关联ID列表（用于后续更新标签计数）
	var deletedLabelIds []string
	result = tx.Model(&modelChat.FriendLabelRelation{}).
		Where("user_id = ? AND friend_id = ?", userId, req.FriendId).
		Distinct("label_id").
		Pluck("label_id", &deletedLabelIds)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 执行硬删除
	result = tx.Unscoped().Where(
		"user_id = ? AND friend_id = ?",
		userId, req.FriendId,
	).Delete(&modelChat.FriendLabelRelation{})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 2. 如果有新标签，则创建新的关联
	if len(req.LabelIds) > 0 {
		// 批量创建新的标签关联
		var relations []*modelChat.FriendLabelRelation
		for _, labelId := range req.LabelIds {
			relation := &modelChat.FriendLabelRelation{
				UserId:    userId,
				FriendId:  req.FriendId,
				LabelId:   labelId,
				CreatedAt: gtimeNow,
				UpdatedAt: gtimeNow,
			}
			relations = append(relations, relation)
		}

		// 批量插入
		if len(relations) > 0 {
			result = tx.Create(&relations)
			if result.Error != nil {
				tx.Rollback()
				return nil, result.Error
			}
		}
	}

	// 3. 更新标签的好友计数
	// 获取所有相关标签ID（包括被删除的和新添加的）
	var allLabelIds []string

	// 合并所有需要更新计数的标签ID
	// deletedLabelIds 已经在前面获取
	allLabelIds = append(allLabelIds, deletedLabelIds...)
	allLabelIds = append(allLabelIds, req.LabelIds...)

	// 去重
	labelIdMap := make(map[string]bool)
	var uniqueLabelIds []string
	for _, id := range allLabelIds {
		if !labelIdMap[id] {
			labelIdMap[id] = true
			uniqueLabelIds = append(uniqueLabelIds, id)
		}
	}

	// 更新每个标签的好友计数
	for _, labelId := range uniqueLabelIds {
		// 计算当前标签关联的好友数量（由于是硬删除，不需要检查 deleted_at）
		var currentCount int64
		countResult := tx.Model(&modelChat.FriendLabelRelation{}).
			Where("user_id = ? AND label_id = ?", userId, labelId).
			Count(&currentCount)

		if countResult.Error != nil {
			tx.Rollback()
			return nil, countResult.Error
		}

		// 更新标签的好友计数
		updateResult := tx.Model(&modelChat.FriendLabel{}).
			Where("label_id = ? AND user_id = ?", labelId, userId).
			Update("friend_count", currentCount)

		if updateResult.Error != nil {
			tx.Rollback()
			return nil, updateResult.Error
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 返回结果
	res = &chat.RelateFriendLabelRes{
		FriendId: req.FriendId,
		LabelIds: req.LabelIds,
	}

	return
}

// LabelRelateFriend 标签批量关联好友 (增加模式)
func (s *ServerFriend) LabelRelateFriend(req *chat.LabelRelateFriendReq, userId string) (res *chat.LabelRelateFriendRes, err error) {
	// 1. 参数校验
	if req.LabelId == "" {
		return nil, errors.New("标签ID不能为空")
	}
	if len(req.FriendIds) == 0 {
		return nil, errors.New("好友ID列表不能为空")
	}

	// 2. 检查标签是否存在
	var label modelChat.FriendLabel
	result := dao.Db.Model(&modelChat.FriendLabel{}).Where(
		"label_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.LabelId, userId,
	).First(&label)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("标签不存在")
		}
		return nil, result.Error
	}

	// 3. 验证好友ID列表是否都是当前用户的好友
	var validFriendIds []string
	result = dao.Db.Model(&modelChat.FriendRelation{}).
		Where("user_id = ? AND friend_id IN ? AND deleted_at IS NULL", userId, req.FriendIds).
		Pluck("friend_id", &validFriendIds)

	if result.Error != nil {
		return nil, result.Error
	}

	if len(validFriendIds) == 0 {
		return nil, errors.New("所有指定的用户都不是您的好友")
	}

	// 检查是否所有好友都有效，如果有无效好友，返回错误
	if len(validFriendIds) != len(req.FriendIds) {
		invalidFriendIds := make([]string, 0)
		validFriendMap := make(map[string]bool)
		for _, id := range validFriendIds {
			validFriendMap[id] = true
		}
		for _, id := range req.FriendIds {
			if !validFriendMap[id] {
				invalidFriendIds = append(invalidFriendIds, id)
			}
		}
		return nil, errors.New("以下用户不是您的好友: " + strings.Join(invalidFriendIds, ", "))
	}

	// 4. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 5. 获取已存在的标签-好友关联
	var existingFriendIds []string
	result = tx.Model(&modelChat.FriendLabelRelation{}).
		Where("user_id = ? AND label_id = ? AND friend_id IN ? AND deleted_at IS NULL",
			userId, req.LabelId, validFriendIds).
		Pluck("friend_id", &existingFriendIds)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 6. 找出需要新增的好友ID
	existingMap := make(map[string]bool, len(existingFriendIds))
	for _, id := range existingFriendIds {
		existingMap[id] = true
	}

	var newFriendIds []string
	for _, id := range validFriendIds {
		if !existingMap[id] {
			newFriendIds = append(newFriendIds, id)
		}
	}

	// 7. 创建新的标签-好友关联
	if len(newFriendIds) > 0 {
		gtimeNow := gtime.Now()
		batchRelations := make([]*modelChat.FriendLabelRelation, 0, len(newFriendIds))

		for _, friendId := range newFriendIds {
			relation := &modelChat.FriendLabelRelation{
				UserId:    userId,
				FriendId:  friendId,
				LabelId:   req.LabelId,
				CreatedAt: gtimeNow,
				UpdatedAt: gtimeNow,
			}
			batchRelations = append(batchRelations, relation)
		}

		// 批量插入
		result = tx.Create(&batchRelations)
		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 8. 更新标签的好友计数
	newCount := len(existingFriendIds) + len(newFriendIds)
	result = tx.Model(&modelChat.FriendLabel{}).
		Where("label_id = ? AND user_id = ?", req.LabelId, userId).
		Update("friend_count", newCount)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 9. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 10. 返回结果
	res = &chat.LabelRelateFriendRes{
		LabelIds: req.LabelId,
		FriendIs: newFriendIds, // 只返回新增的好友ID
	}

	return
}

// FriendsRelateLabels 批量好友关联批量标签(增加不是覆盖)
// 性能优化：
// 1. 使用批量查询减少数据库请求次数
// 2. 使用事务确保数据一致性
// 3. 使用批量插入提高性能
// 4. 避免重复关联
func (s *ServerFriend) FriendsRelateLabels(req *chat.FriendsRelateLabelsReq, userId string) (res *chat.FriendsRelateLabelsRes, err error) {
	// 参数校验
	if len(req.FriendIds) == 0 {
		return nil, errors.New("好友ID列表不能为空")
	}
	if len(req.LabelIds) == 0 {
		return nil, errors.New("标签ID列表不能为空")
	}

	// 添加调试日志
	fmt.Printf("FriendsRelateLabels 请求参数: 用户ID=%s, 好友IDs=%v, 标签IDs=%v\n",
		userId, req.FriendIds, req.LabelIds)

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 1. 验证所有标签是否存在且属于当前用户
	var validLabelCount int64
	result := tx.Model(&modelChat.FriendLabel{}).Where(
		"label_id IN ? AND user_id = ? AND deleted_at IS NULL",
		req.LabelIds, userId,
	).Count(&validLabelCount)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	if int(validLabelCount) != len(req.LabelIds) {
		tx.Rollback()
		return nil, errors.New("部分标签不存在或不属于当前用户")
	}

	fmt.Printf("验证标签成功: 找到 %d 个有效标签\n", validLabelCount)

	// 2. 验证所有好友是否存在且与当前用户有好友关系
	var validFriendIds []string
	result = tx.Model(&modelChat.FriendRelation{}).
		Where("user_id = ? AND friend_id IN ? AND deleted_at IS NULL", userId, req.FriendIds).
		Pluck("friend_id", &validFriendIds)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	if len(validFriendIds) == 0 {
		tx.Rollback()
		return nil, errors.New("没有有效的好友关系")
	}

	fmt.Printf("验证好友成功: 找到 %d 个有效好友\n", len(validFriendIds))

	// 如果部分好友ID无效，记录日志但继续处理有效的好友
	if len(validFriendIds) != len(req.FriendIds) {
		// 创建一个映射来快速检查哪些好友ID是有效的
		validFriendMap := make(map[string]bool, len(validFriendIds))
		for _, id := range validFriendIds {
			validFriendMap[id] = true
		}

		// 找出无效的好友ID
		var invalidFriendIds []string
		for _, id := range req.FriendIds {
			if !validFriendMap[id] {
				invalidFriendIds = append(invalidFriendIds, id)
			}
		}

		// 记录日志但继续处理
		fmt.Printf("部分好友ID无效或不是当前用户的好友: %v\n", invalidFriendIds)
	}

	// 3. 查询已存在的标签-好友关联，避免重复插入
	type LabelFriendPair struct {
		LabelId  string
		FriendId string
	}

	var existingPairs []LabelFriendPair
	result = tx.Model(&modelChat.FriendLabelRelation{}).
		Select("label_id, friend_id").
		Where("user_id = ? AND label_id IN ? AND friend_id IN ?", userId, req.LabelIds, validFriendIds).
		Find(&existingPairs)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	fmt.Printf("已存在的标签-好友关联数量: %d\n", len(existingPairs))

	// 创建一个映射来快速检查哪些关联已经存在
	existingMap := make(map[string]bool, len(existingPairs))
	for _, pair := range existingPairs {
		key := fmt.Sprintf("%d:%s", pair.LabelId, pair.FriendId)
		existingMap[key] = true
		fmt.Printf("已存在的关联: 标签ID=%s, 好友ID=%s\n", pair.LabelId, pair.FriendId)
	}

	// 4. 创建新的标签-好友关联
	now := gtime.Now()
	var newRelations []*modelChat.FriendLabelRelation

	// 为每个标签和每个好友创建关联，但跳过已存在的关联
	totalPossibleRelations := 0
	for _, labelId := range req.LabelIds {
		for _, friendId := range validFriendIds {
			totalPossibleRelations++
			key := fmt.Sprintf("%d:%s", labelId, friendId)
			if !existingMap[key] {
				relation := &modelChat.FriendLabelRelation{
					UserId:    userId,
					FriendId:  friendId,
					LabelId:   labelId,
					CreatedAt: now,
					UpdatedAt: now,
				}
				newRelations = append(newRelations, relation)
				fmt.Printf("准备创建新关联: 标签ID=%d, 好友ID=%s\n", labelId, friendId)
			}
		}
	}

	fmt.Printf("总共可能的关联数: %d, 需要创建的新关联数: %d\n",
		totalPossibleRelations, len(newRelations))

	// 如果有新的关联需要创建
	if len(newRelations) > 0 {
		// 使用批量插入提高性能
		batchSize := 1000 // 每批处理的记录数
		for i := 0; i < len(newRelations); i += batchSize {
			end := i + batchSize
			if end > len(newRelations) {
				end = len(newRelations)
			}

			batch := newRelations[i:end]
			fmt.Printf("批量插入关联: 批次大小=%d\n", len(batch))
			result = tx.Create(&batch)
			if result.Error != nil {
				tx.Rollback()
				fmt.Printf("批量插入失败: %v\n", result.Error)
				return nil, result.Error
			}
		}
		fmt.Printf("批量插入完成: 成功创建 %d 个新关联\n", len(newRelations))
	} else {
		fmt.Printf("没有新的关联需要创建\n")
	}

	// 5. 更新每个标签的好友计数
	for _, labelId := range req.LabelIds {
		// 查询标签当前的好友数量
		var friendCount int64
		result = tx.Model(&modelChat.FriendLabelRelation{}).
			Where("user_id = ? AND label_id = ?", userId, labelId).
			Count(&friendCount)

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}

		fmt.Printf("更新标签好友计数: 标签ID=%d, 好友数量=%d\n", labelId, friendCount)

		// 更新标签的好友计数
		result = tx.Model(&modelChat.FriendLabel{}).
			Where("label_id = ? AND user_id = ?", labelId, userId).
			Update("friend_count", friendCount)

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		fmt.Printf("提交事务失败: %v\n", err)
		return nil, err
	}

	fmt.Printf("事务提交成功\n")

	// 返回结果
	res = &chat.FriendsRelateLabelsRes{
		LabelIds:  req.LabelIds,
		FriendIds: validFriendIds,
		Success:   true,
		Message:   "批量关联成功",
	}

	return
}

// LabelUnrelateFriend 标签批量取消关联好友 (返回取消关联的好友列表)
func (s *ServerFriend) LabelUnrelateFriend(req *chat.LabelUnrelateFriendReq, userId string) (res *chat.LabelUnrelateFriendRes, err error) {
	// 参数校验
	if req.LabelId == "" {
		return nil, errors.New("标签ID不能为空")
	}
	if len(req.FriendIds) == 0 {
		return nil, errors.New("好友ID列表不能为空")
	}

	// 检查标签是否存在
	var label modelChat.FriendLabel
	result := dao.Db.Model(&modelChat.FriendLabel{}).Where(
		"label_id = ? AND user_id = ? AND deleted_at IS NULL",
		req.LabelId, userId,
	).First(&label)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("标签不存在")
		}
		return nil, result.Error
	}

	// 验证好友ID列表是否都是当前用户的好友
	var validFriendIds []string
	if len(req.FriendIds) > 0 {
		// 批量查询好友关系
		result = dao.Db.Model(&modelChat.FriendRelation{}).
			Where("user_id = ? AND friend_id IN ? AND deleted_at IS NULL", userId, req.FriendIds).
			Pluck("friend_id", &validFriendIds)

		if result.Error != nil {
			return nil, result.Error
		}

		// 检查是否所有好友都有效
		if len(validFriendIds) != len(req.FriendIds) {
			// 找出无效的好友ID
			invalidFriendIds := make([]string, 0)
			validFriendMap := make(map[string]bool)

			for _, id := range validFriendIds {
				validFriendMap[id] = true
			}

			for _, id := range req.FriendIds {
				if !validFriendMap[id] {
					invalidFriendIds = append(invalidFriendIds, id)
				}
			}

			return nil, errors.New("以下用户不是您的好友: " + strings.Join(invalidFriendIds, ", "))
		}
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 1. 检查这些好友是否已经关联了该标签
	var existingRelations []modelChat.FriendLabelRelation
	result = tx.Where(
		"user_id = ? AND label_id = ? AND friend_id IN ?",
		userId, req.LabelId, validFriendIds,
	).Find(&existingRelations)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 如果没有找到任何关联，直接返回
	if len(existingRelations) == 0 {
		tx.Rollback()
		return &chat.LabelUnrelateFriendRes{
			LabelId:  req.LabelId,
			FriendIs: []string{},
		}, nil
	}

	// 提取已关联的好友ID
	existingFriendMap := make(map[string]bool)
	var existingFriendIds []string
	for _, relation := range existingRelations {
		existingFriendMap[relation.FriendId] = true
		existingFriendIds = append(existingFriendIds, relation.FriendId)
	}

	// 找出实际需要取消关联的好友ID（交集）
	var friendIdsToUnrelate []string
	for _, id := range validFriendIds {
		if existingFriendMap[id] {
			friendIdsToUnrelate = append(friendIdsToUnrelate, id)
		}
	}

	// 如果没有需要取消关联的好友，直接返回
	if len(friendIdsToUnrelate) == 0 {
		tx.Rollback()
		return &chat.LabelUnrelateFriendRes{
			LabelId:  req.LabelId,
			FriendIs: []string{},
		}, nil
	}

	// 2. 硬删除指定的标签-好友关联
	result = tx.Unscoped().Where(
		"user_id = ? AND label_id = ? AND friend_id IN ?",
		userId, req.LabelId, friendIdsToUnrelate,
	).Delete(&modelChat.FriendLabelRelation{})

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 3. 更新标签的好友计数
	// 计算新的好友数量：当前好友数量减去取消关联的好友数量
	newFriendCount := label.FriendCount - len(friendIdsToUnrelate)
	if newFriendCount < 0 {
		newFriendCount = 0
	}

	result = tx.Model(&modelChat.FriendLabel{}).
		Where("label_id = ? AND user_id = ?", req.LabelId, userId).
		Update("friend_count", newFriendCount)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 返回结果
	res = &chat.LabelUnrelateFriendRes{
		LabelId:  req.LabelId,
		FriendIs: friendIdsToUnrelate,
	}

	return
}

// FriendLabelSort 标签排序
// 主要功能是给标签排序，更新标签的排序顺序
func (s *ServerFriend) FriendLabelSort(req *chat.FriendLabelSortReq, userId string) (res *chat.FriendLabelSortRes, err error) {
	// 1. 参数校验
	if len(req.LabelOrderList) == 0 {
		return nil, errors.New("标签排序列表不能为空")
	}

	// 2. 提取所有标签ID和排序映射
	labelIds := make([]string, 0, len(req.LabelOrderList))
	labelOrderMap := make(map[string]int, len(req.LabelOrderList))
	for _, item := range req.LabelOrderList {
		if item.LabelId == "" {
			return nil, errors.New("标签ID无效")
		}
		labelIds = append(labelIds, item.LabelId)
		labelOrderMap[item.LabelId] = item.LabelOrder
	}

	// 3. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 4. 验证标签所有权
	var validLabels []modelChat.FriendLabel
	result := tx.Model(&modelChat.FriendLabel{}).Where(
		"label_id IN ? AND user_id = ? AND deleted_at IS NULL",
		labelIds, userId,
	).Find(&validLabels)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 5. 检查是否所有标签都存在且属于当前用户
	if len(validLabels) != len(labelIds) {
		tx.Rollback()
		return nil, errors.New("部分标签不存在或不属于您")
	}

	// 6. 性能优化：使用单条SQL批量更新所有标签排序
	now := time.Now()

	// 6.1 预分配结果数组
	updatedLabels := make([]chat.LabelSortInfo, 0, len(validLabels))

	// 6.2 构建批量更新SQL的CASE部分
	var caseSQL strings.Builder
	caseSQL.WriteString("CASE label_id ")

	// 6.3 构建参数列表
	var queryParams []interface{}

	// 6.4 构建CASE语句和结果数组
	for _, label := range validLabels {
		newOrder := labelOrderMap[label.LabelId]
		caseSQL.WriteString("WHEN ? THEN ? ")
		queryParams = append(queryParams, label.LabelId, newOrder)

		// 添加到结果列表
		updatedLabels = append(updatedLabels, chat.LabelSortInfo{
			LabelId:    label.LabelId,
			LabelOrder: newOrder,
		})
	}
	caseSQL.WriteString("ELSE label_order END")

	// 6.5 构建完整SQL
	placeholders := make([]string, len(labelIds))
	for i := range labelIds {
		placeholders[i] = "?"
	}

	sql := fmt.Sprintf(
		"UPDATE friend_labels SET label_order = %s, updated_at = ? WHERE label_id IN (%s) AND user_id = ?",
		caseSQL.String(),
		strings.Join(placeholders, ","),
	)

	// 6.6 添加剩余参数
	queryParams = append(queryParams, now)
	for _, id := range labelIds {
		queryParams = append(queryParams, id)
	}
	queryParams = append(queryParams, userId)

	// 6.7 执行批量更新
	result = tx.Exec(sql, queryParams...)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 7. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 8. 返回结果
	res = &chat.FriendLabelSortRes{
		LabelOrderList: updatedLabels,
	}

	return
}

// getLabelFriendCount 获取标签关联的好友数量
func (s *ServerFriend) getLabelFriendCount(labelId int64) int {
	var count int64
	// 注意：这里假设我们使用的是硬删除，如果使用软删除，需要添加 AND deleted_at IS NULL 条件
	dao.Db.Model(&modelChat.FriendLabelRelation{}).Where(
		"label_id = ?",
		labelId,
	).Count(&count)
	return int(count)
}

// BlockUser 拉黑用户
func (s *ServerFriend) BlockUser(req *chat.BlockUserReq, userId string) (res *chat.BlockUserRes, err error) {
	// 参数校验
	if req.FriendId == "" {
		return nil, errors.New("好友id不能为空")
	}

	// 检查是否是自己
	if req.FriendId == userId {
		return nil, errors.New("不能拉黑自己")
	}

	// 检查是否已经在黑名单中
	var blackCount int64
	result := dao.Db.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND blocked_id = ? AND deleted_at IS NULL",
		userId, req.FriendId,
	).Count(&blackCount)

	if result.Error != nil {
		return nil, result.Error
	}

	if blackCount > 0 {
		return nil, errors.New("该用户已在黑名单中")
	}

	// 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	// 1. 添加到黑名单
	now := gtime.Now()
	blackList := &modelChat.FriendBlackList{
		UserId:      userId,
		BlockedId:   req.FriendId,
		BlockReason: req.BlockReason,
		BlockType:   req.BlockType,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	result = tx.Create(blackList)
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 2. 软删除好友关系（如果存在）, 只删除自己这边的好友关系
	result = tx.Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		userId, req.FriendId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 4. 删除好友标签关联（如果存在）
	result = tx.Model(&modelChat.FriendLabelRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		userId, req.FriendId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 5. 删除好友请求（如果存在）目前是2边多删除了
	result = tx.Model(&modelChat.FriendRequest{}).Where(
		"(sender_id = ? AND receiver_id = ?) OR (receiver_id = ? AND sender_id = ?)",
		userId, req.FriendId, req.FriendId, userId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 提交事务
	tx.Commit()

	// 返回结果
	res = &chat.BlockUserRes{
		Success: true,
		Message: "已将用户加入黑名单",
	}

	return
}

// UnblockUser 取消拉黑用户
// 取消拉黑用户并恢复已软删除的好友关系
func (s *ServerFriend) UnblockUser(req *chat.UnblockUserReq, userId string) (res *chat.UnblockUserRes, err error) {
	// 1. 参数校验
	if req.UserId == "" {
		return nil, errors.New("用户ID不能为空")
	}

	// 2. 检查是否在黑名单中
	var blackList modelChat.FriendBlackList
	result := dao.Db.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND blocked_id = ? AND deleted_at IS NULL",
		userId, req.UserId,
	).First(&blackList)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, errors.New("该用户不在黑名单中")
		}
		return nil, result.Error
	}

	// 3. 开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = errors.New("系统错误")
		}
	}()

	now := gtime.Now()

	// 4. 从黑名单中移除
	result = tx.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND blocked_id = ? AND deleted_at IS NULL",
		userId, req.UserId,
	).Update("deleted_at", now)

	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}

	// 5. 检查是否存在已软删除的好友关系
	// 5.1 检查对方到自己的关系
	var theirRelation modelChat.FriendRelation
	resultMy := tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ? AND deleted_at IS NOT NULL",
		userId, req.UserId,
	).First(&theirRelation)

	// 6. 恢复好友关系
	var restoredRelation bool

	// 6.1 恢复对方到自己的关系
	if resultMy.Error == nil && theirRelation.UserId != "" {
		result = tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
			"user_id = ? AND friend_id = ? AND deleted_at IS NOT NULL",
			userId, req.UserId,
		).Updates(map[string]interface{}{
			"deleted_at": nil,
			"updated_at": now,
		})

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}

		if result.RowsAffected > 0 {
			restoredRelation = true
		}
	}

	// 7. 恢复好友标签关联
	if restoredRelation {
		result = tx.Unscoped().Model(&modelChat.FriendLabelRelation{}).Where(
			"user_id = ? AND friend_id = ? AND deleted_at IS NOT NULL",
			userId, req.UserId,
		).Updates(map[string]interface{}{
			"deleted_at": nil,
		})

		if result.Error != nil {
			tx.Rollback()
			return nil, result.Error
		}
	}

	// 8. 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	// 9. 构建返回结果
	message := "已将用户从黑名单中移除"
	if restoredRelation {
		message += "，并恢复了好友关系"
	}

	res = &chat.UnblockUserRes{
		Success: restoredRelation,
		Message: message,
	}

	return
}

// 20、GetBlockList 获取黑名单列表
func (s *ServerFriend) GetBlockList(req *chat.GetBlockListReq, userId string) (res *chat.GetBlockListRes, err error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 20
	}

	// 获取黑名单总数
	var total int64
	result := dao.Db.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND deleted_at IS NULL",
		userId,
	).Count(&total)

	if result.Error != nil {
		return nil, result.Error
	}

	// 获取黑名单列表
	var blackList []modelChat.FriendBlackList
	result = dao.Db.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND deleted_at IS NULL",
		userId,
	).Order("created_at DESC").
		Offset((req.Page - 1) * req.Size).
		Limit(req.Size).
		Find(&blackList)

	if result.Error != nil {
		return nil, result.Error
	}

	// 构建响应数据
	var blockList []chat.BlockUserInfo
	for _, block := range blackList {
		// 获取被拉黑用户信息
		userNick, userAvatar := s.getUserInfo(block.BlockedId)

		// 构建黑名单信息
		blockInfo := chat.BlockUserInfo{
			UserId:      block.BlockedId,
			UserName:    userNick,
			UserAvatar:  userAvatar,
			BlockReason: block.BlockReason,
			BlockType:   block.BlockType,
			CreatedAt:   tools.GtimeToStringNMDHMS(block.CreatedAt),
		}

		blockList = append(blockList, blockInfo)
	}

	// 返回结果
	res = &chat.GetBlockListRes{
		List:       blockList,
		TotalCount: int(total),
	}

	return
}

// createDirectFriendship 直接建立好友关系（无需验证）
func (s *ServerFriend) createDirectFriendship(userId, friendId string, requestSource int) (*chat.AddFriendRes, error) {
	// 1、开启事务
	tx := dao.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := gtime.Now()

	// 2、检查是否已存在好友关系（包括软删除的）
	var existRelation modelChat.FriendRelation
	resultExist := tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		userId, friendId,
	).First(&existRelation)

	var partyRelation modelChat.FriendRelation
	resultParty := tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
		"user_id = ? AND friend_id = ?",
		friendId, userId,
	).First(&partyRelation)

	// 3、处理用户到好友的关系
	if resultExist.Error == gorm.ErrRecordNotFound {
		// 创建新的好友关系
		relation := &modelChat.FriendRelation{
			UserId:         userId,
			FriendId:       friendId,
			RelationStatus: 1, // 正常
			IsStar:         false,
			IsTop:          false,
			ShardKey:       int(time.Now().Unix() % 10),
			CreatedAt:      now,
			UpdatedAt:      now,
		}
		if err := tx.Create(relation).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if resultExist.Error == nil && existRelation.DeletedAt != nil {
		// 恢复软删除的关系
		if err := tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
			"user_id = ? AND friend_id = ?",
			userId, friendId,
		).Updates(map[string]interface{}{
			"deleted_at": nil,
			"updated_at": now,
		}).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if resultExist.Error != nil {
		tx.Rollback()
		return nil, resultExist.Error
	}

	// 4、处理好友到用户的关系
	if resultParty.Error == gorm.ErrRecordNotFound {
		// 创建新的好友关系
		relation := &modelChat.FriendRelation{
			UserId:         friendId,
			FriendId:       userId,
			RelationStatus: 1, // 正常
			IsStar:         false,
			IsTop:          false,
			ShardKey:       int(time.Now().Unix() % 10),
			CreatedAt:      now,
			UpdatedAt:      now,
		}
		if err := tx.Create(relation).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if resultParty.Error == nil && partyRelation.DeletedAt != nil {
		// 恢复软删除的关系
		if err := tx.Unscoped().Model(&modelChat.FriendRelation{}).Where(
			"user_id = ? AND friend_id = ?",
			friendId, userId,
		).Updates(map[string]interface{}{
			"deleted_at": nil,
			"updated_at": now,
		}).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if resultParty.Error != nil {
		tx.Rollback()
		return nil, resultParty.Error
	}

	// 5、创建好友申请记录（用于记录添加方式）
	friendRequest := &modelChat.FriendRequest{
		SenderId:      userId,
		ReceiverId:    friendId,
		RequestMsg:    "系统自动添加",
		RequestSource: requestSource,
		HandleStatus:  consts.FriendHandlrStatus_agree, // 已同意
		HandleMsg:     "无需验证，自动同意",
		HandleTime:    now,
		CreatedAt:     now,
		UpdatedAt:     now,
		ShardKey:      int(time.Now().Unix() % 10),
	}

	if err := tx.Create(friendRequest).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 6、提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 7、发送好友添加成功消息
	go func() {
		// 异步发送消息，避免影响主流程
		if err := s.addFriendOkMsgPush(userId, "", friendId); err != nil {
			g.Log().Errorf(context.Background(), "发送好友添加成功消息失败: %v", err)
		}
	}()

	// 8、返回结果
	return &chat.AddFriendRes{
		AddStatus: 3,
		Message:   "已成功添加为好友",
	}, nil
}

// handelAddFriendRequest 处理需要验证的好友申请流程
func (s *ServerFriend) handelAddFriendRequest(req *chat.AddFriendReq, userId string) (*chat.AddFriendRes, error) {
	// 1、检查是否已经有未处理的申请
	var existRequest modelChat.FriendRequest
	result := dao.Db.Unscoped().Model(&modelChat.FriendRequest{}).Where(
		"sender_id = ? AND receiver_id = ?",
		userId, req.UserId,
	).First(&existRequest)

	var msgTips string
	if result.Error == nil {
		// 已有申请记录，更新申请信息
		updateData := map[string]interface{}{
			"request_msg":    req.RequestMsg,
			"request_source": req.RequestSource,
			"handle_status":  consts.FriendHandlrStatus_Pending, // 使用常量
			"created_at":     gtime.Now(),
			"handle_msg":     "",
			"deleted_at":     nil,
		}
		result = dao.Db.Unscoped().Model(&modelChat.FriendRequest{}).Where(
			"id = ?",
			existRequest.Id,
		).Updates(updateData)

		if result.Error != nil {
			return nil, result.Error
		}

		msgTips = "您已发送过好友申请，请等待对方处理"

	} else if result.Error != gorm.ErrRecordNotFound {
		// 其他错误
		return nil, result.Error
	} else {
		// 2、创建新的好友申请
		now := gtime.Now()
		friendRequest := &modelChat.FriendRequest{
			SenderId:      userId,
			ReceiverId:    req.UserId,
			RequestMsg:    req.RequestMsg,
			RequestSource: req.RequestSource,
			HandleStatus:  consts.FriendHandlrStatus_Pending, // 使用常量
			CreatedAt:     now,
			UpdatedAt:     now,
			ShardKey:      int(time.Now().Unix() % 10), // 计算分片键
		}

		result = dao.Db.Create(friendRequest)
		if result.Error != nil {
			return nil, result.Error
		}
		msgTips = "好友申请已发送"
	}

	// 3. 发送好友申请给用户
	go s.addFriendRequestMsgPush(req.UserId)

	// 4. 返回结果
	return &chat.AddFriendRes{
		AddStatus: 1,
		Message:   msgTips,
	}, nil
}

// AddFriendOkMsgPush 好友添加成功消息推送与消息入库
func (s *ServerFriend) addFriendOkMsgPush(requestUserId string, requestMsg string, userTarget string) (err error) {

	convId := messageDb.generateConvId(consts.ReceiverType_Private, requestUserId, userTarget)
	// 1、给被申请方发送申请加好友的消息
	if requestMsg != "" {
		objFriendReqMsg := &WebSocketMessage{
			MsgType:     consts.MsgType_Chat,
			MsgClientId: "",

			MsgReceiverType: consts.ReceiverType_Private,
			MsgReceiverId:   userTarget,

			MsgContent:     requestMsg,
			MsgContentType: consts.ConntetType_Text,
			MsgContentFmt:  1,
		}
		//	保存数据库, 只发送给接收者
		_ = s.chatManager.PushServerMsgInfo(objFriendReqMsg, convId, consts.PushStrategy_Receiver, requestUserId, uniqueId.GenerateMessageID())
	}

	// 2、给被申请方发送提示消息："您已添加了xxx，以上是打招呼的消息"
	requestUserName, _ := server.GetUserInfo(requestUserId)
	tipMsgToTarget := &WebSocketMessage{
		MsgType:     consts.MsgType_Chat,
		MsgClientId: "",

		MsgReceiverType: consts.ReceiverType_Private,
		MsgReceiverId:   userTarget,

		MsgContent:     fmt.Sprintf("您已添加了%s，以上是打招呼的消息", requestUserName),
		MsgContentType: consts.ConntetType_AddFriendOk,
		MsgContentFmt:  1,
	}
	//	保存数据库, 只发送给接收者
	_ = s.chatManager.PushServerMsgInfo(tipMsgToTarget, convId, consts.PushStrategy_Receiver, "system", uniqueId.GenerateMessageID())

	// 3、给申请方发送通过验证的消息："我通过了你的朋友验证请求，现在我们可以开始聊天了"
	msgToRequester := &WebSocketMessage{
		MsgType:     consts.MsgType_Chat,
		MsgClientId: "",

		MsgReceiverType: consts.ReceiverType_Private,
		MsgReceiverId:   requestUserId,

		MsgContent:     "我通过了你的朋友验证请求，现在我们可以开始聊天了",
		MsgContentType: consts.ConntetType_Text,
		MsgContentFmt:  1,
	}
	//	保存数据库, 只发送给接收者
	_ = s.chatManager.PushServerMsgInfo(msgToRequester, convId, consts.PushStrategy_Receiver, userTarget, uniqueId.GenerateMessageID())

	// 4、给申请双方发送刷新好友列表信息
	msgToUpdateFriendList := &WebSocketMessage{
		MsgType:     consts.MsgType_ChatNotice,
		MsgClientId: "",

		MsgReceiverType: consts.ReceiverType_Private,
		MsgReceiverId:   requestUserId,

		MsgContent:     "刷新好友通知，不需要显示",
		MsgContentType: consts.ConntetType_FriendListUpdate,
		MsgContentFmt:  1,
	}
	//	不保存数据库
	_ = s.chatManager.PushServerMsgInfo(msgToUpdateFriendList, convId, consts.PushStrategy_All, userTarget, "")

	return nil
}

// addFriendRequestMsgPush 添加好友申请 通知
func (s *ServerFriend) addFriendRequestMsgPush(userId string) {

	nCount := int64(0)
	result := dao.Db.Model(&modelChat.FriendRequest{}).Where(
		"receiver_id = ? AND handle_status = 1",
		userId,
	).Count(&nCount)
	if result.Error == nil {
		requestCount := g.Map{"request_count": nCount}
		// 1、给被申请方发送添加好友申请 通知
		objFriendReqMsg := &WebSocketMessage{
			MsgType:     consts.MsgType_ChatNotice,
			MsgClientId: "",

			MsgReceiverType: consts.ReceiverType_Private,
			MsgReceiverId:   userId,

			MsgContent:     requestCount,
			MsgContentType: consts.MsgContentType_Friend_Request,
			MsgContentFmt:  1,
		}
		//	保存数据库, 只发送给接收者
		_ = s.chatManager.PushServerMsgInfo(objFriendReqMsg, "", consts.PushStrategy_Receiver, "system", "")
	}
}

// getUserInfo 获取用户基本信息（用户名和头像）
func (s *ServerFriend) getUserInfo(userId string) (userNick string, userAvatar string) {
	// 这里应该调用用户服务获取用户信息
	// 为简化实现，这里直接从数据库查询
	var userInfo struct {
		UserNick   string
		UserAvatar string
	}

	// 查询用户信息
	dao.Db.Model(modelUser.UserInfo{}).Select("user_nick, user_avatar").Where("user_id = ?", userId).Scan(&userInfo)

	return userInfo.UserNick, userInfo.UserAvatar
}
